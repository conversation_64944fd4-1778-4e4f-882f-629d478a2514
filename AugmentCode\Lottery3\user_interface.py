"""
用户交互模块
实现用户界面和功能选择逻辑
"""

import os
import pandas as pd
from typing import Optional
from data_loader import DataLoader
from probability_calculator import ProbabilityCalculator
from prediction_algorithms import PredictionAlgorithms
from comparison_analysis import ComparisonAnalysis


class UserInterface:
    """用户界面类，处理用户交互和功能选择"""
    
    def __init__(self):
        """初始化用户界面"""
        self.data = None
        self.prob_calculator = None
        self.prediction_algorithms = None
        self.comparison_analysis = None
        
    def load_data(self) -> bool:
        """
        加载数据
        
        Returns:
            是否加载成功
        """
        try:
            loader = DataLoader()
            self.data = loader.load_data()
            
            if self.data is None:
                print("❌ 数据加载失败！请检查lottery_data_all.xlsx文件是否存在。")
                return False
            
            print(f"✅ 数据加载成功！共加载{len(self.data)}期历史数据。")
            
            # 初始化其他模块
            self.prob_calculator = ProbabilityCalculator(self.data)
            self.prediction_algorithms = PredictionAlgorithms(self.prob_calculator)
            self.comparison_analysis = ComparisonAnalysis(self.data)
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载出错：{str(e)}")
            return False
    
    def show_main_menu(self):
        """显示主菜单"""
        print("\n" + "="*50)
        print("🎯 双色球预测分析系统")
        print("="*50)
        print("1. 📊 查看统计表")
        print("2. 🔮 进行预测")
        print("3. 📈 比对分析")
        print("4. ❓ 帮助说明")
        print("0. 🚪 退出程序")
        print("="*50)
    
    def show_statistics_menu(self):
        """显示统计表菜单"""
        print("\n" + "="*40)
        print("📊 统计表选择")
        print("="*40)
        print("1. 历史出现概率表")
        print("2. 大小球数概率表")
        print("3. 冷热球数概率表")
        print("4. 重号数概率表")
        print("5. 历史跟随性概率矩阵")
        print("6. 大小球跟随性概率矩阵")
        print("7. 冷热球跟随性概率矩阵")
        print("8. 重号跟随性概率矩阵")
        print("0. 返回主菜单")
        print("="*40)
    
    def show_prediction_menu(self):
        """显示预测菜单"""
        print("\n" + "="*40)
        print("🔮 预测选择")
        print("="*40)
        print("1. 第1组：马尔科夫链预测")
        print("2. 第2组：贝叶斯概率预测")
        print("3. 第3组：历史出现概率预测")
        print("4. 第4组：马尔科夫链+大小跟随性")
        print("5. 第5组：马尔科夫链+冷热跟随性")
        print("6. 第6组：马尔科夫链+重号跟随性")
        print("7. 第7组：马尔科夫链+大小概率")
        print("8. 🎯 全部7组预测")
        print("0. 返回主菜单")
        print("="*40)
    
    def show_analysis_menu(self):
        """显示分析菜单"""
        print("\n" + "="*40)
        print("📈 比对分析选择")
        print("="*40)
        print("1. 单期回测")
        print("2. 多期回测")
        print("3. 生成分析报告")
        print("0. 返回主菜单")
        print("="*40)
    
    def handle_statistics(self):
        """处理统计表功能"""
        while True:
            self.show_statistics_menu()
            choice = input("请选择功能 (0-8): ").strip()
            
            if choice == '0':
                break
            elif choice == '1':
                self.show_appearance_probability()
            elif choice == '2':
                self.show_big_ball_probability()
            elif choice == '3':
                self.show_cold_ball_probability()
            elif choice == '4':
                self.show_repeat_ball_probability()
            elif choice == '5':
                self.show_following_probability_matrix()
            elif choice == '6':
                self.show_big_ball_following_matrix()
            elif choice == '7':
                self.show_cold_ball_following_matrix()
            elif choice == '8':
                self.show_repeat_ball_following_matrix()
            else:
                print("❌ 无效选择，请重新输入！")
    
    def handle_prediction(self):
        """处理预测功能"""
        while True:
            self.show_prediction_menu()
            choice = input("请选择预测方法 (0-8): ").strip()
            
            if choice == '0':
                break
            elif choice == '1':
                self.predict_single_group(1)
            elif choice == '2':
                self.predict_single_group(2)
            elif choice == '3':
                self.predict_single_group(3)
            elif choice == '4':
                self.predict_single_group(4)
            elif choice == '5':
                self.predict_single_group(5)
            elif choice == '6':
                self.predict_single_group(6)
            elif choice == '7':
                self.predict_single_group(7)
            elif choice == '8':
                self.predict_all_groups()
            else:
                print("❌ 无效选择，请重新输入！")
    
    def handle_analysis(self):
        """处理比对分析功能"""
        while True:
            self.show_analysis_menu()
            choice = input("请选择分析功能 (0-3): ").strip()
            
            if choice == '0':
                break
            elif choice == '1':
                self.single_period_backtest()
            elif choice == '2':
                self.multiple_period_backtest()
            elif choice == '3':
                self.generate_analysis_report()
            else:
                print("❌ 无效选择，请重新输入！")
    
    def show_appearance_probability(self):
        """显示历史出现概率表"""
        print("\n📊 历史出现概率表")
        print("-" * 50)
        
        red_prob_df, blue_prob_df = self.prob_calculator.calculate_ball_appearance_probability()
        
        print("红球历史出现概率：")
        print(red_prob_df.to_string(index=False))
        
        print("\n蓝球历史出现概率：")
        print(blue_prob_df.to_string(index=False))
        
        input("\n按回车键继续...")
    
    def show_big_ball_probability(self):
        """显示大小球数概率表"""
        print("\n📊 大小球数概率表")
        print("-" * 50)
        
        red_big_df, blue_big_df = self.prob_calculator.calculate_big_ball_count_probability()
        
        print("红球大球数概率：")
        print(red_big_df.to_string(index=False))
        
        print("\n蓝球大球数概率：")
        print(blue_big_df.to_string(index=False))
        
        input("\n按回车键继续...")
    
    def show_cold_ball_probability(self):
        """显示冷热球数概率表"""
        print("\n📊 冷热球数概率表")
        print("-" * 50)
        
        red_cold_df, blue_cold_df = self.prob_calculator.calculate_cold_ball_count_probability()
        
        print("红球冷球数概率：")
        print(red_cold_df.to_string(index=False))
        
        print("\n蓝球冷球数概率：")
        print(blue_cold_df.to_string(index=False))
        
        input("\n按回车键继续...")
    
    def show_repeat_ball_probability(self):
        """显示重号数概率表"""
        print("\n📊 重号数概率表")
        print("-" * 50)
        
        red_repeat_df = self.prob_calculator.calculate_repeat_ball_count_probability()
        
        print("红球重号数概率：")
        print(red_repeat_df.to_string(index=False))
        
        input("\n按回车键继续...")
    
    def show_following_probability_matrix(self):
        """显示历史跟随性概率矩阵"""
        print("\n📊 历史跟随性概率矩阵")
        print("-" * 50)
        print("矩阵较大，建议导出到Excel查看详细数据")
        
        red_matrix, blue_matrix = self.prob_calculator.calculate_ball_following_probability()
        
        print(f"红球跟随性矩阵形状：{red_matrix.shape}")
        print(f"蓝球跟随性矩阵形状：{blue_matrix.shape}")
        
        input("\n按回车键继续...")
    
    def show_big_ball_following_matrix(self):
        """显示大小球跟随性概率矩阵"""
        print("\n📊 大小球跟随性概率矩阵")
        print("-" * 50)
        
        red_matrix, blue_matrix = self.prob_calculator.calculate_big_ball_following_probability()
        
        print("红球大球数跟随性矩阵：")
        red_df = pd.DataFrame(red_matrix, 
                             index=[f'下期{i}个大球' for i in range(7)],
                             columns=[f'本期{i}个大球' for i in range(7)])
        print(red_df.round(4))
        
        print("\n蓝球大球数跟随性矩阵：")
        blue_df = pd.DataFrame(blue_matrix,
                              index=[f'下期{i}个大球' for i in range(2)],
                              columns=[f'本期{i}个大球' for i in range(2)])
        print(blue_df.round(4))
        
        input("\n按回车键继续...")
    
    def show_cold_ball_following_matrix(self):
        """显示冷热球跟随性概率矩阵"""
        print("\n📊 冷热球跟随性概率矩阵")
        print("-" * 50)
        
        red_matrix, blue_matrix = self.prob_calculator.calculate_cold_ball_following_probability()
        
        print("红球冷球数跟随性矩阵：")
        red_df = pd.DataFrame(red_matrix,
                             index=[f'下期{i}个冷球' for i in range(7)],
                             columns=[f'本期{i}个冷球' for i in range(7)])
        print(red_df.round(4))
        
        print("\n蓝球冷球数跟随性矩阵：")
        blue_df = pd.DataFrame(blue_matrix,
                              index=[f'下期{i}个冷球' for i in range(2)],
                              columns=[f'本期{i}个冷球' for i in range(2)])
        print(blue_df.round(4))
        
        input("\n按回车键继续...")
    
    def show_repeat_ball_following_matrix(self):
        """显示重号跟随性概率矩阵"""
        print("\n📊 重号跟随性概率矩阵")
        print("-" * 50)
        
        red_matrix, unique_counts = self.prob_calculator.calculate_repeat_ball_following_probability()
        
        print("红球重号数跟随性矩阵：")
        red_df = pd.DataFrame(red_matrix,
                             index=[f'下期{count}个重号' for count in unique_counts],
                             columns=[f'本期{count}个重号' for count in unique_counts])
        print(red_df.round(4))
        
        input("\n按回车键继续...")

    def predict_single_group(self, group_num: int):
        """
        单组预测

        Args:
            group_num: 组号
        """
        if not self.prediction_algorithms:
            print("❌ 预测算法未初始化！")
            return

        print(f"\n🔮 第{group_num}组预测")
        print("-" * 50)

        # 显示预测信息
        self.prediction_algorithms.print_prediction_info()

        # 获取预测结果
        all_predictions = self.prediction_algorithms.predict_all_groups()
        red_balls, blue_ball = all_predictions[group_num]

        # 算法说明
        algorithm_names = {
            1: "马尔科夫链预测",
            2: "贝叶斯概率预测",
            3: "历史出现概率预测",
            4: "马尔科夫链+大小跟随性",
            5: "马尔科夫链+冷热跟随性",
            6: "马尔科夫链+重号跟随性",
            7: "马尔科夫链+大小概率"
        }

        print(f"\n🎯 {algorithm_names[group_num]}结果：")
        print(f"红球：{' '.join(map(str, red_balls))}")
        print(f"蓝球：{blue_ball}")
        print(f"完整号码：{' '.join(map(str, red_balls))} + {blue_ball}")

        input("\n按回车键继续...")

    def predict_all_groups(self):
        """全部7组预测"""
        if not self.prediction_algorithms:
            print("❌ 预测算法未初始化！")
            return

        print("\n🎯 全部7组预测")
        print("-" * 50)

        # 显示预测信息
        self.prediction_algorithms.print_prediction_info()

        # 获取所有预测结果
        all_predictions = self.prediction_algorithms.predict_all_groups()

        algorithm_names = {
            1: "马尔科夫链预测",
            2: "贝叶斯概率预测",
            3: "历史出现概率预测",
            4: "马尔科夫链+大小跟随性",
            5: "马尔科夫链+冷热跟随性",
            6: "马尔科夫链+重号跟随性",
            7: "马尔科夫链+大小概率"
        }

        print("\n🎯 预测结果汇总：")
        for group_num in range(1, 8):
            red_balls, blue_ball = all_predictions[group_num]
            print(f"第{group_num}组 ({algorithm_names[group_num]})：")
            print(f"  {' '.join(map(str, red_balls))} + {blue_ball}")

        input("\n按回车键继续...")

    def single_period_backtest(self):
        """单期回测"""
        if self.data is None or not self.comparison_analysis:
            print("❌ 数据或分析模块未初始化！")
            return

        print("\n📈 单期回测")
        print("-" * 50)

        # 获取用户输入
        try:
            period_input = input("请输入要回测的期号（如25075）或相对位置（如-1表示最新一期，-2表示倒数第二期）: ").strip()

            if period_input.startswith('-'):
                # 相对位置
                relative_pos = int(period_input)
                if relative_pos >= 0:
                    print("❌ 相对位置应为负数！")
                    return

                period_index = len(self.data) + relative_pos
                if period_index < 0:
                    print("❌ 相对位置超出数据范围！")
                    return
            else:
                # 期号
                period_num = period_input
                period_row = self.data[self.data['期号'] == period_num]
                if period_row.empty:
                    print(f"❌ 未找到期号 {period_num}！")
                    return

                period_index = period_row.index[0]

            # 执行回测
            print(f"正在回测第{period_index}期...")
            results = self.comparison_analysis.backtest_single_period(period_index)

            if not results:
                print("❌ 回测失败！")
                return

            # 显示结果
            test_row = self.data.iloc[period_index]
            actual_red = [int(test_row[f'红球{i}']) for i in range(1, 7)]
            actual_blue = int(test_row['蓝球'])

            print(f"\n📊 期号 {test_row['期号']} 回测结果：")
            print(f"实际开奖：{' '.join(map(str, actual_red))} + {actual_blue}")
            print("-" * 50)

            for group_num in range(1, 8):
                result = results[group_num]
                print(f"第{group_num}组预测：{' '.join(map(str, result['预测红球']))} + {result['预测蓝球']}")
                print(f"  红球命中：{result['红球命中数']}个，蓝球：{'命中' if result['蓝球命中'] else '未命中'}，{result['奖级']}")

        except ValueError:
            print("❌ 输入格式错误！")
        except Exception as e:
            print(f"❌ 回测出错：{str(e)}")

        input("\n按回车键继续...")

    def multiple_period_backtest(self):
        """多期回测"""
        if self.data is None or not self.comparison_analysis:
            print("❌ 数据或分析模块未初始化！")
            return

        print("\n📈 多期回测")
        print("-" * 50)

        try:
            # 获取回测范围
            print("请输入回测范围（建议不超过50期以免等待时间过长）：")
            start_input = input("起始期数（如-50表示倒数第50期）: ").strip()
            end_input = input("结束期数（如-1表示最新一期）: ").strip()

            # 转换为索引
            if start_input.startswith('-'):
                start_index = len(self.data) + int(start_input)
            else:
                start_period = start_input
                start_row = self.data[self.data['期号'] == start_period]
                if start_row.empty:
                    print(f"❌ 未找到起始期号 {start_period}！")
                    return
                start_index = start_row.index[0]

            if end_input.startswith('-'):
                end_index = len(self.data) + int(end_input)
            else:
                end_period = end_input
                end_row = self.data[self.data['期号'] == end_period]
                if end_row.empty:
                    print(f"❌ 未找到结束期号 {end_period}！")
                    return
                end_index = end_row.index[0]

            if start_index >= end_index:
                print("❌ 起始期数应小于结束期数！")
                return

            period_count = end_index - start_index + 1
            if period_count > 100:
                confirm = input(f"⚠️  将回测{period_count}期，可能需要较长时间，是否继续？(y/n): ").strip().lower()
                if confirm != 'y':
                    return

            # 执行回测
            print(f"开始回测{period_count}期...")
            results_df = self.comparison_analysis.backtest_multiple_periods(start_index, end_index)

            if results_df.empty:
                print("❌ 回测失败！")
                return

            # 显示简要统计
            stats_df = self.comparison_analysis.analyze_accuracy_statistics(results_df)

            print(f"\n📊 回测完成！共{period_count}期")
            print("各组平均红球命中数：")
            for _, row in stats_df.iterrows():
                print(f"第{row['组号']}组：{row['平均红球命中数']}个 (蓝球命中率：{row['蓝球命中率']})")

            # 询问是否查看详细报告
            show_detail = input("\n是否查看详细分析报告？(y/n): ").strip().lower()
            if show_detail == 'y':
                report = self.comparison_analysis.generate_comparison_report(results_df)
                print("\n" + report)

        except ValueError:
            print("❌ 输入格式错误！")
        except Exception as e:
            print(f"❌ 回测出错：{str(e)}")

        input("\n按回车键继续...")

    def generate_analysis_report(self):
        """生成分析报告"""
        if self.data is None or not self.comparison_analysis:
            print("❌ 数据或分析模块未初始化！")
            return

        print("\n📈 生成分析报告")
        print("-" * 50)

        try:
            # 获取回测范围
            period_count = input("请输入要分析的期数（建议20-50期）: ").strip()
            period_count = int(period_count)

            if period_count <= 0 or period_count > len(self.data) - 10:
                print("❌ 期数范围无效！")
                return

            # 执行回测
            start_index = len(self.data) - period_count - 1
            end_index = len(self.data) - 2

            print(f"正在分析最近{period_count}期数据...")
            results_df = self.comparison_analysis.backtest_multiple_periods(start_index, end_index)

            if results_df.empty:
                print("❌ 分析失败！")
                return

            # 生成报告
            report = self.comparison_analysis.generate_comparison_report(results_df)
            print("\n" + report)

            # 询问是否保存报告
            save_report = input("是否保存报告到文件？(y/n): ").strip().lower()
            if save_report == 'y':
                filename = f"analysis_report_{period_count}periods.txt"
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(report)
                print(f"✅ 报告已保存到 {filename}")

        except ValueError:
            print("❌ 输入格式错误！")
        except Exception as e:
            print(f"❌ 生成报告出错：{str(e)}")

        input("\n按回车键继续...")

    def show_help(self):
        """显示帮助说明"""
        print("\n❓ 帮助说明")
        print("="*50)
        print("📖 系统功能说明：")
        print()
        print("1. 📊 查看统计表")
        print("   - 查看各种历史概率统计表和跟随性概率矩阵")
        print("   - 包括球号出现概率、大小球分布、冷热球分布等")
        print()
        print("2. 🔮 进行预测")
        print("   - 使用7种不同算法进行号码预测")
        print("   - 基于马尔科夫链、贝叶斯概率等数学模型")
        print()
        print("3. 📈 比对分析")
        print("   - 回测历史预测准确性")
        print("   - 比较不同算法的性能表现")
        print()
        print("🔬 算法说明：")
        print("第1组：马尔科夫链 - 基于状态转移概率")
        print("第2组：贝叶斯概率 - 结合先验和后验概率")
        print("第3组：历史概率 - 基于历史出现频率")
        print("第4组：马尔科夫+大小跟随 - 考虑大小球分布特征")
        print("第5组：马尔科夫+冷热跟随 - 考虑冷热球分布特征")
        print("第6组：马尔科夫+重号跟随 - 考虑重号分布特征")
        print("第7组：马尔科夫+大小概率 - 结合大小球历史概率")
        print()
        print("⚠️  注意事项：")
        print("- 本系统仅供学习研究使用")
        print("- 彩票具有随机性，预测结果仅供参考")
        print("- 请理性购彩，量力而行")

        input("\n按回车键继续...")

    def run(self):
        """运行主程序"""
        print("🎯 双色球预测分析系统启动中...")

        # 加载数据
        if not self.load_data():
            print("程序无法继续运行，请检查数据文件。")
            return

        # 主循环
        while True:
            self.show_main_menu()
            choice = input("请选择功能 (0-4): ").strip()

            if choice == '0':
                print("👋 感谢使用双色球预测分析系统！")
                break
            elif choice == '1':
                self.handle_statistics()
            elif choice == '2':
                self.handle_prediction()
            elif choice == '3':
                self.handle_analysis()
            elif choice == '4':
                self.show_help()
            else:
                print("❌ 无效选择，请重新输入！")


def main():
    """主函数"""
    ui = UserInterface()
    ui.run()


if __name__ == "__main__":
    main()
