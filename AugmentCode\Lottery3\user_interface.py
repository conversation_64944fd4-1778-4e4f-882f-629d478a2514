"""
用户交互模块
实现用户界面和功能选择逻辑
"""

import os
import pandas as pd
from typing import Optional
from data_loader import DataLoader
from probability_calculator import ProbabilityCalculator
from prediction_algorithms import PredictionAlgorithms
from comparison_analysis import ComparisonAnalysis


class UserInterface:
    """用户界面类，处理用户交互和功能选择"""
    
    def __init__(self):
        """初始化用户界面"""
        self.data = None
        self.prob_calculator = None
        self.prediction_algorithms = None
        self.comparison_analysis = None
        
    def load_data(self) -> bool:
        """
        加载数据
        
        Returns:
            是否加载成功
        """
        try:
            loader = DataLoader()
            self.data = loader.load_data()
            
            if self.data is None:
                print("❌ 数据加载失败！请检查lottery_data_all.xlsx文件是否存在。")
                return False
            
            print(f"✅ 数据加载成功！共加载{len(self.data)}期历史数据。")
            
            # 初始化其他模块
            self.prob_calculator = ProbabilityCalculator(self.data)
            self.prediction_algorithms = PredictionAlgorithms(self.prob_calculator)
            self.comparison_analysis = ComparisonAnalysis(self.data)
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载出错：{str(e)}")
            return False
    
    def show_main_menu(self):
        """显示主菜单"""
        print("\n" + "="*50)
        print("🎯 双色球预测分析系统")
        print("="*50)
        print("1.  预测选号")
        print("2. 📈 分析比对")
        print("3. 📊 打印统计表格")
        print("0. 🚪 退出程序")
        print("="*50)
    
    def show_statistics_menu(self):
        """显示统计表菜单"""
        print("\n" + "="*40)
        print("📊 统计表选择")
        print("="*40)
        print("1. 历史出现概率表")
        print("2. 大小球数概率表")
        print("3. 冷热球数概率表")
        print("4. 重号数概率表")
        print("5. 历史跟随性概率矩阵")
        print("6. 大小球跟随性概率矩阵")
        print("7. 冷热球跟随性概率矩阵")
        print("8. 重号跟随性概率矩阵")
        print("0. 返回主菜单")
        print("="*40)
    
    def show_prediction_menu(self):
        """显示预测菜单"""
        print("\n" + "="*40)
        print("🔮 预测选择")
        print("="*40)
        print("1. 第1组：马尔科夫链预测")
        print("2. 第2组：贝叶斯概率预测")
        print("3. 第3组：历史出现概率预测")
        print("4. 第4组：马尔科夫链+大小跟随性")
        print("5. 第5组：马尔科夫链+冷热跟随性")
        print("6. 第6组：马尔科夫链+重号跟随性")
        print("7. 第7组：马尔科夫链+大小概率")
        print("8. 🎯 全部7组预测")
        print("0. 返回主菜单")
        print("="*40)
    
    def show_analysis_menu(self):
        """显示分析菜单"""
        print("\n" + "="*40)
        print("📈 比对分析选择")
        print("="*40)
        print("1. 单期回测")
        print("2. 多期回测")
        print("3. 生成分析报告")
        print("0. 返回主菜单")
        print("="*40)
    
    def handle_statistics(self):
        """处理统计表功能"""
        while True:
            self.show_statistics_menu()
            choice = input("请选择功能 (0-8): ").strip()
            
            if choice == '0':
                break
            elif choice == '1':
                self.show_appearance_probability()
            elif choice == '2':
                self.show_big_ball_probability()
            elif choice == '3':
                self.show_cold_ball_probability()
            elif choice == '4':
                self.show_repeat_ball_probability()
            elif choice == '5':
                self.show_following_probability_matrix()
            elif choice == '6':
                self.show_big_ball_following_matrix()
            elif choice == '7':
                self.show_cold_ball_following_matrix()
            elif choice == '8':
                self.show_repeat_ball_following_matrix()
            else:
                print("❌ 无效选择，请重新输入！")
    
    def handle_prediction(self):
        """处理预测功能"""
        print("\n🔮 预测选号")
        print("-" * 50)

        if not self.prediction_algorithms:
            print("❌ 预测算法未初始化！")
            return

        # 显示预测信息
        self.prediction_algorithms.print_prediction_info()

        # 获取所有预测结果
        all_predictions = self.prediction_algorithms.predict_all_groups()

        algorithm_names = {
            1: "马尔科夫链预测",
            2: "贝叶斯概率预测",
            3: "历史出现概率预测",
            4: "马尔科夫链+大小跟随性",
            5: "马尔科夫链+冷热跟随性",
            6: "马尔科夫链+重号跟随性",
            7: "马尔科夫链+大小概率"
        }

        print("\n🎯 预测结果汇总：")
        for group_num in range(1, 8):
            red_balls, blue_ball = all_predictions[group_num]
            print(f"第{group_num}组 ({algorithm_names[group_num]})：")
            print(f"  {' '.join(map(str, red_balls))} + {blue_ball}")

        input("\n按回车键继续...")
    
    def handle_analysis(self):
        """处理比对分析功能"""
        print("\n📈 分析比对")
        print("-" * 50)

        # 询问用户开始期号
        start_period = input("请输入开始分析的期号（如23001）: ").strip()

        if not start_period:
            print("❌ 期号不能为空！")
            return

        try:
            # 验证期号格式并开始分析
            self.continuous_analysis(start_period)
        except Exception as e:
            print(f"❌ 分析过程出错：{str(e)}")

    def continuous_analysis(self, start_period: str):
        """连续分析比对功能"""
        # 查找开始期号在数据中的位置
        start_index = None
        for i, row in self.data.iterrows():
            if str(row['期号']) == start_period:
                start_index = i
                break

        if start_index is None:
            print(f"❌ 未找到期号 {start_period}！")
            return

        # 计算总分析期数（需要确保每个分析期后面有6期答案数据）
        total_periods = len(self.data) - start_index - 6
        if total_periods <= 0:
            print("❌ 没有足够的数据进行分析！需要分析期号后至少有6期数据作为答案。")
            return

        print(f"📊 开始连续分析，共需分析 {total_periods} 期")
        print(f"从期号 {start_period} 开始分析...")

        # 初始化结果存储
        all_results = []
        prize_stats = {'一等奖': 0, '二等奖': 0, '三等奖': 0, '四等奖': 0, '五等奖': 0, '六等奖': 0, '未中奖': 0}

        try:
            for period_offset in range(total_periods):
                current_index = start_index + period_offset

                # 2.1.2 当前数据库：目标期号及之前的所有期号的数据
                train_data = self.data.iloc[:current_index + 1].copy()

                # 2.2.2 答案数据：目标期号之后的连续6期红蓝球号码
                answer_data = []
                for i in range(1, 7):  # 获取后续6期数据
                    answer_index = current_index + i
                    if answer_index < len(self.data):
                        answer_row = self.data.iloc[answer_index]
                        answer_data.append({
                            '期号': answer_row['期号'],
                            '红球': [int(answer_row[f'红球{j}']) for j in range(1, 7)],
                            '蓝球': int(answer_row['蓝球'])
                        })

                # 创建预测器
                from probability_calculator import ProbabilityCalculator
                from prediction_algorithms import PredictionAlgorithms

                prob_calc = ProbabilityCalculator(train_data)
                pred_algo = PredictionAlgorithms(prob_calc)

                # 获取所有预测结果
                all_predictions = pred_algo.predict_all_groups()

                # 分析每组预测结果
                from comparison_analysis import ComparisonAnalysis
                comp_analysis = ComparisonAnalysis(train_data)

                # 获取当前分析期号
                current_period = self.data.iloc[current_index]['期号']

                period_results = {}
                for group_num in range(1, 8):
                    predicted_red, predicted_blue = all_predictions[group_num]

                    # 使用新的比对算法
                    accuracy = comp_analysis.calculate_accuracy_with_answer_data(
                        predicted_red, predicted_blue, answer_data
                    )

                    period_results[group_num] = {
                        '期号': current_period,
                        '预测红球': predicted_red,
                        '预测蓝球': predicted_blue,
                        **accuracy
                    }

                    # 统计奖级
                    prize_level = accuracy['奖级']
                    if prize_level in prize_stats:
                        prize_stats[prize_level] += 1

                all_results.append(period_results)

                # 每50期显示一次进度
                if (period_offset + 1) % 50 == 0 or period_offset + 1 == total_periods:
                    # 创建一个虚拟的test_row用于显示
                    current_row = self.data.iloc[current_index]
                    self.show_analysis_progress(period_offset + 1, total_periods, train_data, current_row, pred_algo, prize_stats)

        except KeyboardInterrupt:
            print("\n⚠️ 用户中断了分析过程")
            print(f"已完成 {len(all_results)} 期的分析")
        except Exception as e:
            print(f"\n❌ 分析过程中出现错误：{str(e)}")
            print(f"已完成 {len(all_results)} 期的分析")

        # 保存结果到Excel
        if all_results:
            self.save_analysis_results(all_results, start_period)
            print(f"\n✅ 分析完成！结果已保存到Excel文件")
        else:
            print("\n❌ 没有分析结果可保存")

    def show_analysis_progress(self, completed_periods, total_periods, train_data, test_row, pred_algo, prize_stats):
        """显示分析进度信息"""
        print(f"\n📊 已完成 {completed_periods}/{total_periods} 期分析")
        print(f"当前数据库包含 {len(train_data)} 期数据")

        # 显示最新一期信息
        latest_info = pred_algo.latest_info
        red_balls_str = ' '.join(map(str, latest_info['红球']))
        print(f"当前最新一期号码：{latest_info['期号']} {red_balls_str} + {latest_info['蓝球']}")

        # 计算各种统计信息
        try:
            big_ball_stats = pred_algo.prob_calculator.calculate_big_ball_count(latest_info['红球'], latest_info['蓝球'])
            cold_ball_stats = pred_algo.prob_calculator.calculate_cold_ball_count(latest_info['红球'], latest_info['蓝球'])
            repeat_count = pred_algo.prob_calculator.calculate_repeat_ball_count(latest_info['红球'])

            print(f"最新一期红蓝球大球数：{big_ball_stats['红球大球数']}, {big_ball_stats['蓝球大球数']}")
            print(f"最新一期红蓝球冷球数：{cold_ball_stats['红球冷球数']}, {cold_ball_stats['蓝球冷球数']}")
            print(f"最新一期红球重号数：{repeat_count}")
        except AttributeError:
            print("统计信息计算功能暂不可用")

        # 显示奖级分布
        print("奖级分布统计：")
        for prize, count in prize_stats.items():
            if count > 0:
                print(f"  {prize}：{count}次")

    def save_analysis_results(self, all_results, start_period):
        """
        保存分析结果到Excel文件
        按照要求只保存：分析的期号、分列保存预测的每组红蓝球号码、
        每一组预测号码的红蓝球号码最大命中情况、在最大命中情况下的蓝球号码命中状态与奖级分布
        """
        import pandas as pd
        from datetime import datetime

        # 准备数据
        rows = []
        for period_results in all_results:
            for group_num in range(1, 8):
                result = period_results[group_num]
                row = {
                    '分析期号': result['期号'],
                    '预测组别': f'第{group_num}组',
                    '预测红球1': result['预测红球'][0],
                    '预测红球2': result['预测红球'][1],
                    '预测红球3': result['预测红球'][2],
                    '预测红球4': result['预测红球'][3],
                    '预测红球5': result['预测红球'][4],
                    '预测红球6': result['预测红球'][5],
                    '预测蓝球': result['预测蓝球'],
                    '最大命中数': result['最大命中数'],
                    '蓝球命中状态': result['蓝球命中状态'],
                    '奖级': result['奖级']
                }
                rows.append(row)

        # 创建DataFrame
        df = pd.DataFrame(rows)

        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"分析比对结果_{start_period}_{timestamp}.xlsx"

        # 保存到Excel
        df.to_excel(filename, index=False, engine='openpyxl')
        print(f"结果已保存到：{filename}")

    def handle_statistics_export(self):
        """处理统计表格导出功能"""
        print("\n📊 导出统计表格")
        print("-" * 50)

        if not self.prob_calculator:
            print("❌ 概率计算器未初始化！")
            return

        try:
            self.export_all_statistics()
            print("✅ 所有统计表格已导出到Excel文件")
        except Exception as e:
            print(f"❌ 导出过程出错：{str(e)}")

    def export_all_statistics(self):
        """导出所有统计表格到Excel"""
        import pandas as pd
        from datetime import datetime

        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"统计表格汇总_{timestamp}.xlsx"

        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 导出基础统计信息
                basic_stats = {
                    '总期数': [len(self.data)],
                    '最新期号': [self.data.iloc[-1]['期号']],
                    '数据起始期号': [self.data.iloc[0]['期号']],
                    '数据结束期号': [self.data.iloc[-1]['期号']]
                }
                pd.DataFrame(basic_stats).to_excel(writer, sheet_name='基础统计信息', index=False)

                # 导出红球出现频率统计
                red_freq = {}
                for i in range(1, 34):
                    count = 0
                    for col in [f'红球{j}' for j in range(1, 7)]:
                        count += (self.data[col] == i).sum()
                    red_freq[f'红球{i}'] = [count, count/len(self.data)]

                red_freq_df = pd.DataFrame.from_dict(red_freq, orient='index', columns=['出现次数', '出现频率'])
                red_freq_df.to_excel(writer, sheet_name='红球出现频率', index=True)

                # 导出蓝球出现频率统计
                blue_freq = {}
                for i in range(1, 17):
                    count = (self.data['蓝球'] == i).sum()
                    blue_freq[f'蓝球{i}'] = [count, count/len(self.data)]

                blue_freq_df = pd.DataFrame.from_dict(blue_freq, orient='index', columns=['出现次数', '出现频率'])
                blue_freq_df.to_excel(writer, sheet_name='蓝球出现频率', index=True)

                # 导出最近100期数据
                recent_data = self.data.tail(100).copy()
                recent_data.to_excel(writer, sheet_name='最近100期数据', index=False)

                print(f"统计表格已保存到：{filename}")

        except Exception as e:
            print(f"导出过程中出现错误：{str(e)}")
            # 创建简化版本
            basic_stats = {
                '总期数': [len(self.data)],
                '最新期号': [self.data.iloc[-1]['期号']],
                '数据起始期号': [self.data.iloc[0]['期号']],
                '数据结束期号': [self.data.iloc[-1]['期号']]
            }
            pd.DataFrame(basic_stats).to_excel(filename, index=False)
            print(f"基础统计表格已保存到：{filename}")

    def show_appearance_probability(self):
        """显示历史出现概率表"""
        print("\n📊 历史出现概率表")
        print("-" * 50)
        
        red_prob_df, blue_prob_df = self.prob_calculator.calculate_ball_appearance_probability()
        
        print("红球历史出现概率：")
        print(red_prob_df.to_string(index=False))
        
        print("\n蓝球历史出现概率：")
        print(blue_prob_df.to_string(index=False))
        
        input("\n按回车键继续...")
    
    def show_big_ball_probability(self):
        """显示大小球数概率表"""
        print("\n📊 大小球数概率表")
        print("-" * 50)
        
        red_big_df, blue_big_df = self.prob_calculator.calculate_big_ball_count_probability()
        
        print("红球大球数概率：")
        print(red_big_df.to_string(index=False))
        
        print("\n蓝球大球数概率：")
        print(blue_big_df.to_string(index=False))
        
        input("\n按回车键继续...")
    
    def show_cold_ball_probability(self):
        """显示冷热球数概率表"""
        print("\n📊 冷热球数概率表")
        print("-" * 50)
        
        red_cold_df, blue_cold_df = self.prob_calculator.calculate_cold_ball_count_probability()
        
        print("红球冷球数概率：")
        print(red_cold_df.to_string(index=False))
        
        print("\n蓝球冷球数概率：")
        print(blue_cold_df.to_string(index=False))
        
        input("\n按回车键继续...")
    
    def show_repeat_ball_probability(self):
        """显示重号数概率表"""
        print("\n📊 重号数概率表")
        print("-" * 50)
        
        red_repeat_df = self.prob_calculator.calculate_repeat_ball_count_probability()
        
        print("红球重号数概率：")
        print(red_repeat_df.to_string(index=False))
        
        input("\n按回车键继续...")
    
    def show_following_probability_matrix(self):
        """显示历史跟随性概率矩阵"""
        print("\n📊 历史跟随性概率矩阵")
        print("-" * 50)
        print("矩阵较大，建议导出到Excel查看详细数据")
        
        red_matrix, blue_matrix = self.prob_calculator.calculate_ball_following_probability()
        
        print(f"红球跟随性矩阵形状：{red_matrix.shape}")
        print(f"蓝球跟随性矩阵形状：{blue_matrix.shape}")
        
        input("\n按回车键继续...")
    
    def show_big_ball_following_matrix(self):
        """显示大小球跟随性概率矩阵"""
        print("\n📊 大小球跟随性概率矩阵")
        print("-" * 50)
        
        red_matrix, blue_matrix = self.prob_calculator.calculate_big_ball_following_probability()
        
        print("红球大球数跟随性矩阵：")
        red_df = pd.DataFrame(red_matrix, 
                             index=[f'下期{i}个大球' for i in range(7)],
                             columns=[f'本期{i}个大球' for i in range(7)])
        print(red_df.round(4))
        
        print("\n蓝球大球数跟随性矩阵：")
        blue_df = pd.DataFrame(blue_matrix,
                              index=[f'下期{i}个大球' for i in range(2)],
                              columns=[f'本期{i}个大球' for i in range(2)])
        print(blue_df.round(4))
        
        input("\n按回车键继续...")
    
    def show_cold_ball_following_matrix(self):
        """显示冷热球跟随性概率矩阵"""
        print("\n📊 冷热球跟随性概率矩阵")
        print("-" * 50)
        
        red_matrix, blue_matrix = self.prob_calculator.calculate_cold_ball_following_probability()
        
        print("红球冷球数跟随性矩阵：")
        red_df = pd.DataFrame(red_matrix,
                             index=[f'下期{i}个冷球' for i in range(7)],
                             columns=[f'本期{i}个冷球' for i in range(7)])
        print(red_df.round(4))
        
        print("\n蓝球冷球数跟随性矩阵：")
        blue_df = pd.DataFrame(blue_matrix,
                              index=[f'下期{i}个冷球' for i in range(2)],
                              columns=[f'本期{i}个冷球' for i in range(2)])
        print(blue_df.round(4))
        
        input("\n按回车键继续...")
    
    def show_repeat_ball_following_matrix(self):
        """显示重号跟随性概率矩阵"""
        print("\n📊 重号跟随性概率矩阵")
        print("-" * 50)
        
        red_matrix, unique_counts = self.prob_calculator.calculate_repeat_ball_following_probability()
        
        print("红球重号数跟随性矩阵：")
        red_df = pd.DataFrame(red_matrix,
                             index=[f'下期{count}个重号' for count in unique_counts],
                             columns=[f'本期{count}个重号' for count in unique_counts])
        print(red_df.round(4))
        
        input("\n按回车键继续...")

    def predict_single_group(self, group_num: int):
        """
        单组预测

        Args:
            group_num: 组号
        """
        if not self.prediction_algorithms:
            print("❌ 预测算法未初始化！")
            return

        print(f"\n🔮 第{group_num}组预测")
        print("-" * 50)

        # 显示预测信息
        self.prediction_algorithms.print_prediction_info()

        # 获取预测结果
        all_predictions = self.prediction_algorithms.predict_all_groups()
        red_balls, blue_ball = all_predictions[group_num]

        # 算法说明
        algorithm_names = {
            1: "马尔科夫链预测",
            2: "贝叶斯概率预测",
            3: "历史出现概率预测",
            4: "马尔科夫链+大小跟随性",
            5: "马尔科夫链+冷热跟随性",
            6: "马尔科夫链+重号跟随性",
            7: "马尔科夫链+大小概率"
        }

        print(f"\n🎯 {algorithm_names[group_num]}结果：")
        print(f"红球：{' '.join(map(str, red_balls))}")
        print(f"蓝球：{blue_ball}")
        print(f"完整号码：{' '.join(map(str, red_balls))} + {blue_ball}")

        input("\n按回车键继续...")

    def predict_all_groups(self):
        """全部7组预测"""
        if not self.prediction_algorithms:
            print("❌ 预测算法未初始化！")
            return

        print("\n🎯 全部7组预测")
        print("-" * 50)

        # 显示预测信息
        self.prediction_algorithms.print_prediction_info()

        # 获取所有预测结果
        all_predictions = self.prediction_algorithms.predict_all_groups()

        algorithm_names = {
            1: "马尔科夫链预测",
            2: "贝叶斯概率预测",
            3: "历史出现概率预测",
            4: "马尔科夫链+大小跟随性",
            5: "马尔科夫链+冷热跟随性",
            6: "马尔科夫链+重号跟随性",
            7: "马尔科夫链+大小概率"
        }

        print("\n🎯 预测结果汇总：")
        for group_num in range(1, 8):
            red_balls, blue_ball = all_predictions[group_num]
            print(f"第{group_num}组 ({algorithm_names[group_num]})：")
            print(f"  {' '.join(map(str, red_balls))} + {blue_ball}")

        input("\n按回车键继续...")

    def single_period_backtest(self):
        """单期回测"""
        if self.data is None or not self.comparison_analysis:
            print("❌ 数据或分析模块未初始化！")
            return

        print("\n📈 单期回测")
        print("-" * 50)

        # 获取用户输入
        try:
            period_input = input("请输入要回测的期号（如25075）或相对位置（如-1表示最新一期，-2表示倒数第二期）: ").strip()

            if period_input.startswith('-'):
                # 相对位置
                relative_pos = int(period_input)
                if relative_pos >= 0:
                    print("❌ 相对位置应为负数！")
                    return

                period_index = len(self.data) + relative_pos
                if period_index < 0:
                    print("❌ 相对位置超出数据范围！")
                    return
            else:
                # 期号
                period_num = period_input
                period_row = self.data[self.data['期号'] == period_num]
                if period_row.empty:
                    print(f"❌ 未找到期号 {period_num}！")
                    return

                period_index = period_row.index[0]

            # 执行回测
            print(f"正在回测第{period_index}期...")
            results = self.comparison_analysis.backtest_single_period(period_index)

            if not results:
                print("❌ 回测失败！")
                return

            # 显示结果
            test_row = self.data.iloc[period_index]
            actual_red = [int(test_row[f'红球{i}']) for i in range(1, 7)]
            actual_blue = int(test_row['蓝球'])

            print(f"\n📊 期号 {test_row['期号']} 回测结果：")
            print(f"实际开奖：{' '.join(map(str, actual_red))} + {actual_blue}")
            print("-" * 50)

            for group_num in range(1, 8):
                result = results[group_num]
                print(f"第{group_num}组预测：{' '.join(map(str, result['预测红球']))} + {result['预测蓝球']}")
                print(f"  红球命中：{result['红球命中数']}个，蓝球：{'命中' if result['蓝球命中'] else '未命中'}，{result['奖级']}")

        except ValueError:
            print("❌ 输入格式错误！")
        except Exception as e:
            print(f"❌ 回测出错：{str(e)}")

        input("\n按回车键继续...")

    def multiple_period_backtest(self):
        """多期回测"""
        if self.data is None or not self.comparison_analysis:
            print("❌ 数据或分析模块未初始化！")
            return

        print("\n📈 多期回测")
        print("-" * 50)

        try:
            # 获取回测范围
            print("请输入回测范围（建议不超过50期以免等待时间过长）：")
            start_input = input("起始期数（如-50表示倒数第50期）: ").strip()
            end_input = input("结束期数（如-1表示最新一期）: ").strip()

            # 转换为索引
            if start_input.startswith('-'):
                start_index = len(self.data) + int(start_input)
            else:
                start_period = start_input
                start_row = self.data[self.data['期号'] == start_period]
                if start_row.empty:
                    print(f"❌ 未找到起始期号 {start_period}！")
                    return
                start_index = start_row.index[0]

            if end_input.startswith('-'):
                end_index = len(self.data) + int(end_input)
            else:
                end_period = end_input
                end_row = self.data[self.data['期号'] == end_period]
                if end_row.empty:
                    print(f"❌ 未找到结束期号 {end_period}！")
                    return
                end_index = end_row.index[0]

            if start_index >= end_index:
                print("❌ 起始期数应小于结束期数！")
                return

            period_count = end_index - start_index + 1
            if period_count > 100:
                confirm = input(f"⚠️  将回测{period_count}期，可能需要较长时间，是否继续？(y/n): ").strip().lower()
                if confirm != 'y':
                    return

            # 执行回测
            print(f"开始回测{period_count}期...")
            results_df = self.comparison_analysis.backtest_multiple_periods(start_index, end_index)

            if results_df.empty:
                print("❌ 回测失败！")
                return

            # 显示简要统计
            stats_df = self.comparison_analysis.analyze_accuracy_statistics(results_df)

            print(f"\n📊 回测完成！共{period_count}期")
            print("各组平均红球命中数：")
            for _, row in stats_df.iterrows():
                print(f"第{row['组号']}组：{row['平均红球命中数']}个 (蓝球命中率：{row['蓝球命中率']})")

            # 询问是否查看详细报告
            show_detail = input("\n是否查看详细分析报告？(y/n): ").strip().lower()
            if show_detail == 'y':
                report = self.comparison_analysis.generate_comparison_report(results_df)
                print("\n" + report)

        except ValueError:
            print("❌ 输入格式错误！")
        except Exception as e:
            print(f"❌ 回测出错：{str(e)}")

        input("\n按回车键继续...")

    def generate_analysis_report(self):
        """生成分析报告"""
        if self.data is None or not self.comparison_analysis:
            print("❌ 数据或分析模块未初始化！")
            return

        print("\n📈 生成分析报告")
        print("-" * 50)

        try:
            # 获取回测范围
            period_count = input("请输入要分析的期数（建议20-50期）: ").strip()
            period_count = int(period_count)

            if period_count <= 0 or period_count > len(self.data) - 10:
                print("❌ 期数范围无效！")
                return

            # 执行回测
            start_index = len(self.data) - period_count - 1
            end_index = len(self.data) - 2

            print(f"正在分析最近{period_count}期数据...")
            results_df = self.comparison_analysis.backtest_multiple_periods(start_index, end_index)

            if results_df.empty:
                print("❌ 分析失败！")
                return

            # 生成报告
            report = self.comparison_analysis.generate_comparison_report(results_df)
            print("\n" + report)

            # 询问是否保存报告
            save_report = input("是否保存报告到文件？(y/n): ").strip().lower()
            if save_report == 'y':
                filename = f"analysis_report_{period_count}periods.txt"
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(report)
                print(f"✅ 报告已保存到 {filename}")

        except ValueError:
            print("❌ 输入格式错误！")
        except Exception as e:
            print(f"❌ 生成报告出错：{str(e)}")

        input("\n按回车键继续...")

    def show_help(self):
        """显示帮助说明"""
        print("\n❓ 帮助说明")
        print("="*50)
        print("📖 系统功能说明：")
        print()
        print("1. 📊 查看统计表")
        print("   - 查看各种历史概率统计表和跟随性概率矩阵")
        print("   - 包括球号出现概率、大小球分布、冷热球分布等")
        print()
        print("2. 🔮 进行预测")
        print("   - 使用7种不同算法进行号码预测")
        print("   - 基于马尔科夫链、贝叶斯概率等数学模型")
        print()
        print("3. 📈 比对分析")
        print("   - 回测历史预测准确性")
        print("   - 比较不同算法的性能表现")
        print()
        print("🔬 算法说明：")
        print("第1组：马尔科夫链 - 基于状态转移概率")
        print("第2组：贝叶斯概率 - 结合先验和后验概率")
        print("第3组：历史概率 - 基于历史出现频率")
        print("第4组：马尔科夫+大小跟随 - 考虑大小球分布特征")
        print("第5组：马尔科夫+冷热跟随 - 考虑冷热球分布特征")
        print("第6组：马尔科夫+重号跟随 - 考虑重号分布特征")
        print("第7组：马尔科夫+大小概率 - 结合大小球历史概率")
        print()
        print("⚠️  注意事项：")
        print("- 本系统仅供学习研究使用")
        print("- 彩票具有随机性，预测结果仅供参考")
        print("- 请理性购彩，量力而行")

        input("\n按回车键继续...")

    def run(self):
        """运行主程序"""
        print("🎯 双色球预测分析系统启动中...")

        # 加载数据
        if not self.load_data():
            print("程序无法继续运行，请检查数据文件。")
            return

        # 主循环
        while True:
            self.show_main_menu()
            choice = input("请选择功能 (0-3): ").strip()

            if choice == '0':
                print("👋 感谢使用双色球预测分析系统！")
                break
            elif choice == '1':
                self.handle_prediction()
            elif choice == '2':
                self.handle_analysis()
            elif choice == '3':
                self.handle_statistics_export()
            else:
                print("❌ 无效选择，请重新输入！")


def main():
    """主函数"""
    ui = UserInterface()
    ui.run()


if __name__ == "__main__":
    main()
