"""
比较分析模块
实现回测功能，比较不同预测算法的准确性
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Union
from data_loader import DataLoader
from probability_calculator import ProbabilityCalculator
from prediction_algorithms import PredictionAlgorithms


class ComparisonAnalysis:
    """比较分析类，实现回测和准确性分析"""
    
    def __init__(self, data: pd.DataFrame):
        """
        初始化比较分析
        
        Args:
            data: 历史数据
        """
        self.data = data
        self.results = []
        
    def calculate_accuracy(self, predicted_red: List[int], predicted_blue: int,
                          actual_red: List[int], actual_blue: int) -> Dict[str, Union[int, str]]:
        """
        计算预测准确性
        
        Args:
            predicted_red: 预测红球
            predicted_blue: 预测蓝球
            actual_red: 实际红球
            actual_blue: 实际蓝球
            
        Returns:
            准确性统计
        """
        # 红球命中数
        red_hits = len(set(predicted_red) & set(actual_red))
        
        # 蓝球命中
        blue_hit = 1 if predicted_blue == actual_blue else 0
        
        # 计算奖级
        prize_level = self._calculate_prize_level(red_hits, blue_hit)
        
        return {
            '红球命中数': red_hits,
            '蓝球命中': blue_hit,
            '奖级': prize_level
        }
    
    def _calculate_prize_level(self, red_hits: int, blue_hit: int) -> str:
        """
        根据命中数计算奖级
        
        Args:
            red_hits: 红球命中数
            blue_hit: 蓝球命中数
            
        Returns:
            奖级字符串
        """
        if red_hits == 6 and blue_hit == 1:
            return "一等奖"
        elif red_hits == 6 and blue_hit == 0:
            return "二等奖"
        elif red_hits == 5 and blue_hit == 1:
            return "三等奖"
        elif (red_hits == 5 and blue_hit == 0) or (red_hits == 4 and blue_hit == 1):
            return "四等奖"
        elif (red_hits == 4 and blue_hit == 0) or (red_hits == 3 and blue_hit == 1):
            return "五等奖"
        elif blue_hit == 1:
            return "六等奖"
        else:
            return "未中奖"
    
    def backtest_single_period(self, test_period_index: int) -> Dict[int, Dict]:
        """
        对单期进行回测
        
        Args:
            test_period_index: 测试期在数据中的索引
            
        Returns:
            各组预测结果
        """
        if test_period_index <= 0 or test_period_index >= len(self.data):
            return {}
        
        # 使用测试期之前的数据进行预测
        train_data = self.data.iloc[:test_period_index].copy()
        test_row = self.data.iloc[test_period_index]
        
        # 获取实际结果
        actual_red = [int(test_row[f'红球{i}']) for i in range(1, 7)]
        actual_blue = int(test_row['蓝球'])
        
        # 创建预测器
        prob_calc = ProbabilityCalculator(train_data)
        pred_algo = PredictionAlgorithms(prob_calc)
        
        # 获取所有组预测
        all_predictions = pred_algo.predict_all_groups()
        
        # 计算每组准确性
        results = {}
        for group_num, (predicted_red, predicted_blue) in all_predictions.items():
            accuracy = self.calculate_accuracy(predicted_red, predicted_blue, actual_red, actual_blue)
            results[group_num] = {
                '期号': test_row['期号'],
                '预测红球': predicted_red,
                '预测蓝球': predicted_blue,
                '实际红球': actual_red,
                '实际蓝球': actual_blue,
                **accuracy
            }
        
        return results
    
    def backtest_multiple_periods(self, start_index: int, end_index: int) -> pd.DataFrame:
        """
        对多期进行回测
        
        Args:
            start_index: 开始索引
            end_index: 结束索引
            
        Returns:
            回测结果DataFrame
        """
        all_results = []
        
        print(f"开始回测，从第{start_index}期到第{end_index}期...")
        
        for i in range(start_index, min(end_index + 1, len(self.data))):
            if i % 10 == 0:
                print(f"正在回测第{i}期...")
            
            period_results = self.backtest_single_period(i)
            
            for group_num, result in period_results.items():
                result['组号'] = group_num
                all_results.append(result)
        
        if not all_results:
            return pd.DataFrame()
        
        df = pd.DataFrame(all_results)
        return df
    
    def analyze_accuracy_statistics(self, results_df: pd.DataFrame) -> pd.DataFrame:
        """
        分析准确性统计
        
        Args:
            results_df: 回测结果DataFrame
            
        Returns:
            统计结果DataFrame
        """
        if results_df.empty:
            return pd.DataFrame()
        
        stats = []
        
        for group_num in sorted(results_df['组号'].unique()):
            group_data = results_df[results_df['组号'] == group_num]
            
            total_periods = len(group_data)
            
            # 红球命中统计
            red_hit_stats = {}
            for i in range(7):
                red_hit_stats[f'红球命中{i}个'] = len(group_data[group_data['红球命中数'] == i])
                red_hit_stats[f'红球命中{i}个占比'] = f"{red_hit_stats[f'红球命中{i}个'] / total_periods * 100:.2f}%"
            
            # 蓝球命中统计
            blue_hits = len(group_data[group_data['蓝球命中'] == 1])
            blue_hit_rate = blue_hits / total_periods * 100
            
            # 奖级统计
            prize_stats = {}
            for prize in ["一等奖", "二等奖", "三等奖", "四等奖", "五等奖", "六等奖", "未中奖"]:
                count = len(group_data[group_data['奖级'] == prize])
                prize_stats[f'{prize}次数'] = count
                prize_stats[f'{prize}占比'] = f"{count / total_periods * 100:.2f}%"
            
            # 平均红球命中数
            avg_red_hits = group_data['红球命中数'].mean()
            
            stat = {
                '组号': group_num,
                '总期数': total_periods,
                '平均红球命中数': f"{avg_red_hits:.2f}",
                '蓝球命中次数': blue_hits,
                '蓝球命中率': f"{blue_hit_rate:.2f}%",
                **red_hit_stats,
                **prize_stats
            }
            
            stats.append(stat)
        
        return pd.DataFrame(stats)
    
    def generate_comparison_report(self, results_df: pd.DataFrame) -> str:
        """
        生成比较报告
        
        Args:
            results_df: 回测结果DataFrame
            
        Returns:
            报告字符串
        """
        if results_df.empty:
            return "无回测数据"
        
        stats_df = self.analyze_accuracy_statistics(results_df)
        
        report = "=== 预测算法比较分析报告 ===\n\n"
        
        # 总体统计
        total_periods = len(results_df) // 7  # 7组算法
        report += f"回测期数：{total_periods}期\n"
        report += f"测试算法：7组\n\n"
        
        # 各组详细统计
        for _, row in stats_df.iterrows():
            group_num = row['组号']
            report += f"第{group_num}组算法统计：\n"
            report += f"  平均红球命中数：{row['平均红球命中数']}\n"
            report += f"  蓝球命中率：{row['蓝球命中率']}\n"
            
            # 红球命中分布
            report += "  红球命中分布：\n"
            for i in range(7):
                report += f"    命中{i}个：{row[f'红球命中{i}个']}次 ({row[f'红球命中{i}个占比']})\n"
            
            # 奖级分布
            report += "  奖级分布：\n"
            for prize in ["一等奖", "二等奖", "三等奖", "四等奖", "五等奖", "六等奖"]:
                if row[f'{prize}次数'] > 0:
                    report += f"    {prize}：{row[f'{prize}次数']}次 ({row[f'{prize}占比']})\n"
            
            report += "\n"
        
        # 排名分析
        stats_df['平均红球命中数_float'] = stats_df['平均红球命中数'].astype(float)
        stats_df['蓝球命中率_float'] = stats_df['蓝球命中率'].str.rstrip('%').astype(float)
        
        best_red = stats_df.loc[stats_df['平均红球命中数_float'].idxmax()]
        best_blue = stats_df.loc[stats_df['蓝球命中率_float'].idxmax()]
        
        report += "=== 性能排名 ===\n"
        report += f"红球命中率最高：第{best_red['组号']}组 (平均{best_red['平均红球命中数']}个)\n"
        report += f"蓝球命中率最高：第{best_blue['组号']}组 ({best_blue['蓝球命中率']})\n"
        
        return report


def test_comparison_analysis():
    """测试比较分析功能"""
    # 加载数据
    loader = DataLoader()
    data = loader.load_data()
    
    if data is None:
        print("数据加载失败")
        return
    
    print("=== 测试比较分析 ===")
    
    # 创建比较分析器
    analyzer = ComparisonAnalysis(data)
    
    # 测试单期回测
    print("测试单期回测...")
    single_result = analyzer.backtest_single_period(-2)  # 倒数第二期
    
    if single_result:
        print("单期回测结果：")
        for group_num, result in single_result.items():
            print(f"第{group_num}组：红球命中{result['红球命中数']}个，蓝球{'命中' if result['蓝球命中'] else '未命中'}，{result['奖级']}")
    
    # 测试多期回测（最近10期）
    print("\n测试多期回测（最近10期）...")
    start_idx = max(0, len(data) - 12)
    end_idx = len(data) - 2
    
    results_df = analyzer.backtest_multiple_periods(start_idx, end_idx)
    
    if not results_df.empty:
        print(f"回测完成，共{len(results_df)}条记录")
        
        # 生成报告
        report = analyzer.generate_comparison_report(results_df)
        print("\n" + report)
    
    print("比较分析测试完成!")


if __name__ == "__main__":
    test_comparison_analysis()
