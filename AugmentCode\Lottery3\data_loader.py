"""
数据读取模块
负责从Excel文件读取双色球历史数据，清理无效数据，建立原始数据库
"""

import pandas as pd
import numpy as np
from typing import Tuple, List, Dict, Any
import os


class DataLoader:
    """数据加载器类，负责读取和处理彩票历史数据"""
    
    def __init__(self, file_path: str = "lottery_data_all.xlsx"):
        """
        初始化数据加载器
        
        Args:
            file_path: Excel文件路径
        """
        self.file_path = file_path
        self.ssqhistory_allout = None
        
    def load_data(self) -> pd.DataFrame:
        """
        读取Excel文件中的双色球数据
        
        Returns:
            处理后的数据DataFrame，包含期号、红球1-6、蓝球列
        """
        try:
            # 读取指定Excel文件的SSQ_data_all标签页
            df = pd.read_excel(self.file_path, sheet_name='SSQ_data_all')
            
            # 选择A列（期号）和I列至O列（红球1-6，蓝球）
            # 假设列名为：A列=期号，I-N列=红球1-6，O列=蓝球
            columns_to_read = ['期号'] + [f'红球{i}' for i in range(1, 7)] + ['蓝球']
            
            # 如果列名不匹配，尝试按位置读取
            if not all(col in df.columns for col in columns_to_read):
                # 按位置读取：A列(0), I-O列(8-14)
                selected_columns = [0] + list(range(8, 15))
                df_selected = df.iloc[:, selected_columns]
                df_selected.columns = ['期号'] + [f'红球{i}' for i in range(1, 7)] + ['蓝球']
            else:
                df_selected = df[columns_to_read]
            
            # 清理数据
            df_cleaned = self._clean_data(df_selected)

            # 按期号排序（从小到大）
            df_cleaned['_sort_key'] = self._sort_periods(df_cleaned['期号'])
            df_cleaned = df_cleaned.sort_values('_sort_key').drop('_sort_key', axis=1).reset_index(drop=True)
            
            self.ssqhistory_allout = df_cleaned
            return df_cleaned
            
        except Exception as e:
            print(f"读取数据文件时出错: {e}")
            return None
    
    def _clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清理无效数据和空数据行
        
        Args:
            df: 原始数据DataFrame
            
        Returns:
            清理后的数据DataFrame
        """
        # 删除包含空值的行
        df_cleaned = df.dropna()
        
        # 删除期号为空或无效的行
        df_cleaned = df_cleaned[df_cleaned['期号'].notna()]
        
        # 确保红球号码在1-33范围内，蓝球号码在1-16范围内
        red_ball_cols = [f'红球{i}' for i in range(1, 7)]
        blue_ball_col = '蓝球'
        
        # 检查红球号码范围
        for col in red_ball_cols:
            df_cleaned = df_cleaned[
                (df_cleaned[col] >= 1) & (df_cleaned[col] <= 33)
            ]
        
        # 检查蓝球号码范围
        df_cleaned = df_cleaned[
            (df_cleaned[blue_ball_col] >= 1) & (df_cleaned[blue_ball_col] <= 16)
        ]
        
        # 确保数据类型正确
        df_cleaned['期号'] = df_cleaned['期号'].astype(str)
        for col in red_ball_cols + [blue_ball_col]:
            df_cleaned[col] = df_cleaned[col].astype(int)

        return df_cleaned.reset_index(drop=True)

    def _sort_periods(self, periods: pd.Series) -> pd.Series:
        """
        对期号进行正确排序
        期号格式：YYNNN (YY=年份, NNN=期数)

        Args:
            periods: 期号Series

        Returns:
            排序后的期号Series
        """
        # 将期号转换为可排序的格式
        def period_to_sort_key(period_str):
            period_str = str(period_str).zfill(5)  # 确保5位数
            year = int(period_str[:2])
            number = int(period_str[2:])
            # 处理年份跨度问题，假设00-30为20xx年，31-99为19xx年
            if year <= 30:
                year += 2000
            else:
                year += 1900
            return year * 1000 + number

        return periods.map(period_to_sort_key)
    
    def get_data(self) -> pd.DataFrame:
        """
        获取已加载的数据
        
        Returns:
            数据DataFrame
        """
        if self.ssqhistory_allout is None:
            return self.load_data()
        return self.ssqhistory_allout
    
    def get_period_range(self) -> Tuple[str, str]:
        """
        获取数据的期号范围
        
        Returns:
            (最小期号, 最大期号)
        """
        if self.ssqhistory_allout is None:
            self.load_data()
        
        if self.ssqhistory_allout is not None and len(self.ssqhistory_allout) > 0:
            min_period = self.ssqhistory_allout['期号'].min()
            max_period = self.ssqhistory_allout['期号'].max()
            return min_period, max_period
        return None, None
    
    def get_data_by_period(self, end_period: str) -> pd.DataFrame:
        """
        获取指定期号及之前的所有数据

        Args:
            end_period: 结束期号

        Returns:
            筛选后的数据DataFrame
        """
        if self.ssqhistory_allout is None:
            self.load_data()

        if self.ssqhistory_allout is not None:
            # 使用排序键进行比较
            end_sort_key = self._sort_periods(pd.Series([end_period])).iloc[0]
            data_sort_keys = self._sort_periods(self.ssqhistory_allout['期号'])
            mask = data_sort_keys <= end_sort_key
            return self.ssqhistory_allout[mask].copy()
        return None


def test_data_loader():
    """测试数据加载器功能"""
    loader = DataLoader()
    data = loader.load_data()
    
    if data is not None:
        print(f"成功加载数据，共 {len(data)} 行")
        print(f"数据列名: {list(data.columns)}")
        print(f"期号范围: {loader.get_period_range()}")
        print("\n前5行数据:")
        print(data.head())
        print("\n后5行数据:")
        print(data.tail())
    else:
        print("数据加载失败")


if __name__ == "__main__":
    test_data_loader()
