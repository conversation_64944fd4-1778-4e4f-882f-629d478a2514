"""
预测算法模块
实现7组不同的预测算法
"""

import numpy as np
import pandas as pd
from typing import Tuple, List, Dict
from probability_calculator import ProbabilityCalculator
from core_algorithms import CoreAlgorithms


class PredictionAlgorithms:
    """预测算法类，实现7种不同的预测策略"""
    
    def __init__(self, prob_calculator: ProbabilityCalculator):
        """
        初始化预测算法
        
        Args:
            prob_calculator: 概率计算器实例
        """
        self.prob_calculator = prob_calculator
        self.core_algo = CoreAlgorithms(prob_calculator)
        self.latest_info = prob_calculator.get_latest_period_info()
        
        # 获取各种概率表和矩阵
        self.red_prob_df, self.blue_prob_df = prob_calculator.calculate_ball_appearance_probability()
        self.red_big_prob_df, self.blue_big_prob_df = prob_calculator.calculate_big_ball_count_probability()
        self.red_cold_prob_df, self.blue_cold_prob_df = prob_calculator.calculate_cold_ball_count_probability()
        self.red_repeat_prob_df = prob_calculator.calculate_repeat_ball_count_probability()
        
        # 获取跟随性概率矩阵
        self.red_follow_matrix, self.blue_follow_matrix = prob_calculator.calculate_ball_following_probability()
        self.red_big_follow_matrix, self.blue_big_follow_matrix = prob_calculator.calculate_big_ball_following_probability()
        self.red_cold_follow_matrix, self.blue_cold_follow_matrix = prob_calculator.calculate_cold_ball_following_probability()
        self.red_repeat_follow_matrix, self.red_repeat_counts = prob_calculator.calculate_repeat_ball_following_probability()
        
    def predict_group1_markov(self) -> Tuple[List[int], int]:
        """
        第1组预测算法：马尔科夫链
        
        Returns:
            (红球号码列表, 蓝球号码)
        """
        return self.core_algo.predict_markov_numbers()
    
    def predict_group2_bayesian(self) -> Tuple[List[int], int]:
        """
        第2组预测算法：贝叶斯概率
        
        Returns:
            (红球号码列表, 蓝球号码)
        """
        return self.core_algo.predict_bayesian_numbers()
    
    def predict_group3_historical(self) -> Tuple[List[int], int]:
        """
        第3组预测算法：历史出现概率
        
        Returns:
            (红球号码列表, 蓝球号码)
        """
        return self.core_algo.predict_historical_numbers()
    
    def _get_predicted_big_ball_counts_following(self) -> Tuple[int, int]:
        """
        基于历史跟随性概率预测红蓝球大球数
        
        Returns:
            (预测红球大球数, 预测蓝球大球数)
        """
        if not self.latest_info:
            return 3, 0  # 默认值
        
        current_red_big = self.latest_info['红球大球数']
        current_blue_big = self.latest_info['蓝球大球数']
        
        # 红球大球数预测
        red_big_probs = self.red_big_follow_matrix[:, current_red_big]
        predicted_red_big = np.argmax(red_big_probs)
        
        # 蓝球大球数预测
        blue_big_probs = self.blue_big_follow_matrix[:, current_blue_big]
        predicted_blue_big = np.argmax(blue_big_probs)
        
        return predicted_red_big, predicted_blue_big
    
    def _get_predicted_cold_ball_counts_following(self) -> Tuple[int, int]:
        """
        基于历史跟随性概率预测红蓝球冷球数
        
        Returns:
            (预测红球冷球数, 预测蓝球冷球数)
        """
        if not self.latest_info:
            return 1, 0  # 默认值
        
        current_red_cold = self.latest_info['红球冷球数']
        current_blue_cold = self.latest_info['蓝球冷球数']
        
        # 红球冷球数预测
        red_cold_probs = self.red_cold_follow_matrix[:, current_red_cold]
        predicted_red_cold = np.argmax(red_cold_probs)
        
        # 蓝球冷球数预测
        blue_cold_probs = self.blue_cold_follow_matrix[:, current_blue_cold]
        predicted_blue_cold = np.argmax(blue_cold_probs)
        
        return predicted_red_cold, predicted_blue_cold
    
    def _get_predicted_repeat_count_following(self) -> int:
        """
        基于历史跟随性概率预测红球重号数
        
        Returns:
            预测红球重号数
        """
        if not self.latest_info:
            return 1  # 默认值
        
        current_repeat = self.latest_info['红球重号数']
        
        # 找到当前重号数在unique_counts中的索引
        if current_repeat in self.red_repeat_counts:
            current_index = self.red_repeat_counts.index(current_repeat)
            repeat_probs = self.red_repeat_follow_matrix[:, current_index]
            predicted_index = np.argmax(repeat_probs)
            predicted_repeat = self.red_repeat_counts[predicted_index]
        else:
            predicted_repeat = 1  # 默认值
        
        return predicted_repeat
    
    def _get_predicted_big_ball_counts_historical(self) -> Tuple[int, int]:
        """
        基于历史出现概率预测红蓝球大球数
        
        Returns:
            (预测红球大球数, 预测蓝球大球数)
        """
        # 红球大球数预测
        red_big_max_idx = self.red_big_prob_df['历史出现概率'].idxmax()
        predicted_red_big = self.red_big_prob_df.loc[red_big_max_idx, '红球大球数']
        
        # 蓝球大球数预测
        blue_big_max_idx = self.blue_big_prob_df['历史出现概率'].idxmax()
        predicted_blue_big = self.blue_big_prob_df.loc[blue_big_max_idx, '蓝球大球数']
        
        return predicted_red_big, predicted_blue_big
    
    def _get_predicted_cold_ball_counts_historical(self) -> Tuple[int, int]:
        """
        基于历史出现概率预测红蓝球冷球数
        
        Returns:
            (预测红球冷球数, 预测蓝球冷球数)
        """
        # 红球冷球数预测
        red_cold_max_idx = self.red_cold_prob_df['历史出现概率'].idxmax()
        predicted_red_cold = self.red_cold_prob_df.loc[red_cold_max_idx, '红球冷球数']
        
        # 蓝球冷球数预测
        blue_cold_max_idx = self.blue_cold_prob_df['历史出现概率'].idxmax()
        predicted_blue_cold = self.blue_cold_prob_df.loc[blue_cold_max_idx, '蓝球冷球数']
        
        return predicted_red_cold, predicted_blue_cold
    
    def _is_big_ball(self, ball_number: int, is_red: bool) -> bool:
        """
        判断是否为大球
        
        Args:
            ball_number: 球号码
            is_red: 是否为红球
            
        Returns:
            是否为大球
        """
        if is_red:
            return ball_number > 16
        else:
            return ball_number > 8
    
    def _is_cold_ball(self, ball_number: int, is_red: bool) -> bool:
        """
        判断是否为冷球（在前5期中未出现）
        
        Args:
            ball_number: 球号码
            is_red: 是否为红球
            
        Returns:
            是否为冷球
        """
        data = self.prob_calculator.data
        if len(data) < 6:
            return False
        
        # 获取前5期数据
        previous_5_rows = data.iloc[-6:-1]
        
        if is_red:
            # 检查红球是否在前5期中出现
            red_ball_cols = [f'红球{i}' for i in range(1, 7)]
            previous_red_balls = set()
            for _, row in previous_5_rows.iterrows():
                previous_red_balls.update([row[col] for col in red_ball_cols])
            return ball_number not in previous_red_balls
        else:
            # 检查蓝球是否在前5期中出现
            previous_blue_balls = set(previous_5_rows['蓝球'].tolist())
            return ball_number not in previous_blue_balls
    
    def _is_repeat_ball(self, ball_number: int) -> bool:
        """
        判断红球是否为重号（与上期相同）
        
        Args:
            ball_number: 红球号码
            
        Returns:
            是否为重号
        """
        if not self.latest_info:
            return False
        
        latest_red_balls = self.latest_info['红球']
        return ball_number in latest_red_balls

    def _adjust_balls_by_big_count(self, red_balls: List[int], blue_ball: int,
                                   target_red_big: int, target_blue_big: int,
                                   use_markov: bool = True) -> Tuple[List[int], int]:
        """
        根据大球数要求调整球号码

        Args:
            red_balls: 初始红球列表
            blue_ball: 初始蓝球
            target_red_big: 目标红球大球数
            target_blue_big: 目标蓝球大球数
            use_markov: 是否使用马尔科夫链概率排序

        Returns:
            调整后的(红球列表, 蓝球)
        """
        # 获取概率用于排序
        if use_markov:
            red_probs, blue_probs = self.core_algo.calculate_markov_chain_probabilities()
        else:
            red_probs = self.red_prob_df['历史出现概率'].values
            blue_probs = self.blue_prob_df['历史出现概率'].values

        # 调整红球
        current_red_big = sum(1 for ball in red_balls if self._is_big_ball(ball, True))

        if current_red_big > target_red_big:
            # 移除多余的大球
            big_balls = [ball for ball in red_balls if self._is_big_ball(ball, True)]
            small_balls = [ball for ball in red_balls if not self._is_big_ball(ball, True)]

            # 按概率排序，移除概率小的大球
            big_balls_with_prob = [(ball, red_probs[ball-1]) for ball in big_balls]
            big_balls_with_prob.sort(key=lambda x: x[1])

            # 移除多余的大球
            remove_count = current_red_big - target_red_big
            for i in range(remove_count):
                if big_balls_with_prob:
                    ball_to_remove = big_balls_with_prob.pop(0)[0]
                    red_balls.remove(ball_to_remove)

            # 补充小球
            all_small_balls = [i for i in range(1, 34) if not self._is_big_ball(i, True) and i not in red_balls]
            small_balls_with_prob = [(ball, red_probs[ball-1]) for ball in all_small_balls]
            small_balls_with_prob.sort(key=lambda x: x[1], reverse=True)

            for i in range(remove_count):
                if small_balls_with_prob and len(red_balls) < 6:
                    red_balls.append(small_balls_with_prob.pop(0)[0])

        elif current_red_big < target_red_big:
            # 添加大球
            all_big_balls = [i for i in range(17, 34) if i not in red_balls]
            big_balls_with_prob = [(ball, red_probs[ball-1]) for ball in all_big_balls]
            big_balls_with_prob.sort(key=lambda x: x[1], reverse=True)

            add_count = target_red_big - current_red_big

            # 先添加大球
            for i in range(add_count):
                if big_balls_with_prob and len(red_balls) < 6:
                    red_balls.append(big_balls_with_prob.pop(0)[0])

            # 如果红球超过6个，移除概率小的小球
            if len(red_balls) > 6:
                small_balls = [ball for ball in red_balls if not self._is_big_ball(ball, True)]
                small_balls_with_prob = [(ball, red_probs[ball-1]) for ball in small_balls]
                small_balls_with_prob.sort(key=lambda x: x[1])

                remove_count = len(red_balls) - 6
                for i in range(remove_count):
                    if small_balls_with_prob:
                        ball_to_remove = small_balls_with_prob.pop(0)[0]
                        red_balls.remove(ball_to_remove)

        # 调整蓝球
        current_blue_big = 1 if self._is_big_ball(blue_ball, False) else 0

        if current_blue_big != target_blue_big:
            if target_blue_big == 1:
                # 需要大球
                big_blue_balls = [i for i in range(9, 17)]
                big_blue_with_prob = [(ball, blue_probs[ball-1]) for ball in big_blue_balls]
                big_blue_with_prob.sort(key=lambda x: x[1], reverse=True)
                if big_blue_with_prob:
                    blue_ball = big_blue_with_prob[0][0]
            else:
                # 需要小球
                small_blue_balls = [i for i in range(1, 9)]
                small_blue_with_prob = [(ball, blue_probs[ball-1]) for ball in small_blue_balls]
                small_blue_with_prob.sort(key=lambda x: x[1], reverse=True)
                if small_blue_with_prob:
                    blue_ball = small_blue_with_prob[0][0]

        return sorted(red_balls), blue_ball

    def _adjust_balls_by_cold_count(self, red_balls: List[int], blue_ball: int,
                                    target_red_cold: int, target_blue_cold: int,
                                    use_markov: bool = True) -> Tuple[List[int], int]:
        """
        根据冷球数要求调整球号码

        Args:
            red_balls: 初始红球列表
            blue_ball: 初始蓝球
            target_red_cold: 目标红球冷球数
            target_blue_cold: 目标蓝球冷球数
            use_markov: 是否使用马尔科夫链概率排序

        Returns:
            调整后的(红球列表, 蓝球)
        """
        # 获取概率用于排序
        if use_markov:
            red_probs, blue_probs = self.core_algo.calculate_markov_chain_probabilities()
        else:
            red_probs = self.red_prob_df['历史出现概率'].values
            blue_probs = self.blue_prob_df['历史出现概率'].values

        # 调整红球
        current_red_cold = sum(1 for ball in red_balls if self._is_cold_ball(ball, True))

        if current_red_cold > target_red_cold:
            # 移除多余的冷球
            cold_balls = [ball for ball in red_balls if self._is_cold_ball(ball, True)]
            hot_balls = [ball for ball in red_balls if not self._is_cold_ball(ball, True)]

            # 按概率排序，移除概率小的冷球
            cold_balls_with_prob = [(ball, red_probs[ball-1]) for ball in cold_balls]
            cold_balls_with_prob.sort(key=lambda x: x[1])

            # 移除多余的冷球
            remove_count = current_red_cold - target_red_cold
            for i in range(remove_count):
                if cold_balls_with_prob:
                    ball_to_remove = cold_balls_with_prob.pop(0)[0]
                    red_balls.remove(ball_to_remove)

            # 补充热球
            all_hot_balls = [i for i in range(1, 34) if not self._is_cold_ball(i, True) and i not in red_balls]
            hot_balls_with_prob = [(ball, red_probs[ball-1]) for ball in all_hot_balls]
            hot_balls_with_prob.sort(key=lambda x: x[1], reverse=True)

            for i in range(remove_count):
                if hot_balls_with_prob and len(red_balls) < 6:
                    red_balls.append(hot_balls_with_prob.pop(0)[0])

        elif current_red_cold < target_red_cold:
            # 添加冷球
            all_cold_balls = [i for i in range(1, 34) if self._is_cold_ball(i, True) and i not in red_balls]
            cold_balls_with_prob = [(ball, red_probs[ball-1]) for ball in all_cold_balls]
            cold_balls_with_prob.sort(key=lambda x: x[1], reverse=True)

            add_count = target_red_cold - current_red_cold

            # 先添加冷球
            for i in range(add_count):
                if cold_balls_with_prob and len(red_balls) < 6:
                    red_balls.append(cold_balls_with_prob.pop(0)[0])

            # 如果红球超过6个，移除概率小的热球
            if len(red_balls) > 6:
                hot_balls = [ball for ball in red_balls if not self._is_cold_ball(ball, True)]
                hot_balls_with_prob = [(ball, red_probs[ball-1]) for ball in hot_balls]
                hot_balls_with_prob.sort(key=lambda x: x[1])

                remove_count = len(red_balls) - 6
                for i in range(remove_count):
                    if hot_balls_with_prob:
                        ball_to_remove = hot_balls_with_prob.pop(0)[0]
                        red_balls.remove(ball_to_remove)

        # 调整蓝球
        current_blue_cold = 1 if self._is_cold_ball(blue_ball, False) else 0

        if current_blue_cold != target_blue_cold:
            if target_blue_cold == 1:
                # 需要冷球
                cold_blue_balls = [i for i in range(1, 17) if self._is_cold_ball(i, False)]
                if use_markov:
                    cold_blue_with_prob = [(ball, blue_probs[ball-1]) for ball in cold_blue_balls]
                else:
                    cold_blue_with_prob = [(ball, blue_probs[ball-1]) for ball in cold_blue_balls]
                cold_blue_with_prob.sort(key=lambda x: x[1], reverse=True)
                if cold_blue_with_prob:
                    blue_ball = cold_blue_with_prob[0][0]
            else:
                # 需要热球
                hot_blue_balls = [i for i in range(1, 17) if not self._is_cold_ball(i, False)]
                if use_markov:
                    hot_blue_with_prob = [(ball, blue_probs[ball-1]) for ball in hot_blue_balls]
                else:
                    hot_blue_with_prob = [(ball, blue_probs[ball-1]) for ball in hot_blue_balls]
                hot_blue_with_prob.sort(key=lambda x: x[1], reverse=True)
                if hot_blue_with_prob:
                    blue_ball = hot_blue_with_prob[0][0]

        return sorted(red_balls), blue_ball

    def _adjust_balls_by_repeat_count(self, red_balls: List[int], blue_ball: int,
                                      target_red_repeat: int, target_blue_cold: int,
                                      use_markov: bool = True) -> Tuple[List[int], int]:
        """
        根据重号数要求调整红球号码，蓝球按冷球数调整

        Args:
            red_balls: 初始红球列表
            blue_ball: 初始蓝球
            target_red_repeat: 目标红球重号数
            target_blue_cold: 目标蓝球冷球数
            use_markov: 是否使用马尔科夫链概率排序

        Returns:
            调整后的(红球列表, 蓝球)
        """
        # 获取概率用于排序
        if use_markov:
            red_probs, _ = self.core_algo.calculate_markov_chain_probabilities()
        else:
            red_probs = self.red_prob_df['历史出现概率'].values

        # 调整红球重号数
        current_red_repeat = sum(1 for ball in red_balls if self._is_repeat_ball(ball))

        if current_red_repeat > target_red_repeat:
            # 移除多余的重号
            repeat_balls = [ball for ball in red_balls if self._is_repeat_ball(ball)]
            non_repeat_balls = [ball for ball in red_balls if not self._is_repeat_ball(ball)]

            # 按概率排序，移除概率小的重号
            repeat_balls_with_prob = [(ball, red_probs[ball-1]) for ball in repeat_balls]
            repeat_balls_with_prob.sort(key=lambda x: x[1])

            # 移除多余的重号
            remove_count = current_red_repeat - target_red_repeat
            for i in range(remove_count):
                if repeat_balls_with_prob:
                    ball_to_remove = repeat_balls_with_prob.pop(0)[0]
                    red_balls.remove(ball_to_remove)

            # 补充非重号
            all_non_repeat_balls = [i for i in range(1, 34) if not self._is_repeat_ball(i) and i not in red_balls]
            non_repeat_balls_with_prob = [(ball, red_probs[ball-1]) for ball in all_non_repeat_balls]
            non_repeat_balls_with_prob.sort(key=lambda x: x[1], reverse=True)

            for i in range(remove_count):
                if non_repeat_balls_with_prob and len(red_balls) < 6:
                    red_balls.append(non_repeat_balls_with_prob.pop(0)[0])

        elif current_red_repeat < target_red_repeat:
            # 添加重号
            all_repeat_balls = [ball for ball in self.latest_info['红球'] if ball not in red_balls]
            repeat_balls_with_prob = [(ball, float(red_probs[ball-1])) for ball in all_repeat_balls]
            repeat_balls_with_prob.sort(key=lambda x: x[1], reverse=True)

            add_count = target_red_repeat - current_red_repeat

            # 先添加重号
            for i in range(add_count):
                if repeat_balls_with_prob and len(red_balls) < 6:
                    red_balls.append(repeat_balls_with_prob.pop(0)[0])

            # 如果红球超过6个，移除概率小的非重号
            if len(red_balls) > 6:
                non_repeat_balls = [ball for ball in red_balls if not self._is_repeat_ball(ball)]
                non_repeat_balls_with_prob = [(ball, red_probs[ball-1]) for ball in non_repeat_balls]
                non_repeat_balls_with_prob.sort(key=lambda x: x[1])

                remove_count = len(red_balls) - 6
                for i in range(remove_count):
                    if non_repeat_balls_with_prob:
                        ball_to_remove = non_repeat_balls_with_prob.pop(0)[0]
                        red_balls.remove(ball_to_remove)

        # 调整蓝球（按冷球数）
        current_blue_cold = 1 if self._is_cold_ball(blue_ball, False) else 0

        if current_blue_cold != target_blue_cold:
            blue_probs = self.blue_prob_df['历史出现概率'].values

            if target_blue_cold == 1:
                # 需要冷球
                cold_blue_balls = [i for i in range(1, 17) if self._is_cold_ball(i, False)]
                cold_blue_with_prob = [(ball, blue_probs[ball-1]) for ball in cold_blue_balls]
                cold_blue_with_prob.sort(key=lambda x: x[1], reverse=True)
                if cold_blue_with_prob:
                    blue_ball = cold_blue_with_prob[0][0]
            else:
                # 需要热球
                hot_blue_balls = [i for i in range(1, 17) if not self._is_cold_ball(i, False)]
                hot_blue_with_prob = [(ball, blue_probs[ball-1]) for ball in hot_blue_balls]
                hot_blue_with_prob.sort(key=lambda x: x[1], reverse=True)
                if hot_blue_with_prob:
                    blue_ball = hot_blue_with_prob[0][0]

        return sorted(red_balls), blue_ball

    def predict_group4_markov_big_following(self) -> Tuple[List[int], int]:
        """
        第4组预测算法：马尔科夫链+大小跟随性特性筛选

        Returns:
            (红球号码列表, 蓝球号码)
        """
        # 获取基于跟随性的大球数预测
        target_red_big, target_blue_big = self._get_predicted_big_ball_counts_following()

        # 先用马尔科夫链获取初始预测
        red_balls, blue_ball = self.predict_group1_markov()

        # 根据大球数要求调整
        red_balls, blue_ball = self._adjust_balls_by_big_count(
            red_balls, blue_ball, target_red_big, target_blue_big, use_markov=True
        )

        return red_balls, blue_ball

    def predict_group5_markov_cold_following(self) -> Tuple[List[int], int]:
        """
        第5组预测算法：马尔科夫链+冷热跟随性特性筛选

        Returns:
            (红球号码列表, 蓝球号码)
        """
        # 获取基于跟随性的冷球数预测
        target_red_cold, target_blue_cold = self._get_predicted_cold_ball_counts_following()

        # 先用马尔科夫链获取初始预测
        red_balls, blue_ball = self.predict_group1_markov()

        # 根据冷球数要求调整
        red_balls, blue_ball = self._adjust_balls_by_cold_count(
            red_balls, blue_ball, target_red_cold, target_blue_cold, use_markov=True
        )

        return red_balls, blue_ball

    def predict_group6_markov_repeat_following(self) -> Tuple[List[int], int]:
        """
        第6组预测算法：马尔科夫链+红球重号跟随性特性筛选

        Returns:
            (红球号码列表, 蓝球号码)
        """
        # 获取基于跟随性的重号数预测和基于历史概率的蓝球冷球数预测
        target_red_repeat = self._get_predicted_repeat_count_following()
        target_blue_cold, _ = self._get_predicted_cold_ball_counts_historical()

        # 先用马尔科夫链获取初始预测
        red_balls, blue_ball = self.predict_group1_markov()

        # 根据重号数和蓝球冷球数要求调整
        red_balls, blue_ball = self._adjust_balls_by_repeat_count(
            red_balls, blue_ball, target_red_repeat, target_blue_cold, use_markov=True
        )

        return red_balls, blue_ball

    def predict_group7_markov_big_historical(self) -> Tuple[List[int], int]:
        """
        第7组预测算法：马尔科夫链+大小概率特性筛选

        Returns:
            (红球号码列表, 蓝球号码)
        """
        # 获取基于历史概率的大球数预测
        target_red_big, target_blue_big = self._get_predicted_big_ball_counts_historical()

        # 先用马尔科夫链获取初始预测
        red_balls, blue_ball = self.predict_group1_markov()

        # 根据大球数要求调整（使用历史概率排序）
        red_balls, blue_ball = self._adjust_balls_by_big_count(
            red_balls, blue_ball, target_red_big, target_blue_big, use_markov=False
        )

        return red_balls, blue_ball

    def predict_all_groups(self) -> Dict[int, Tuple[List[int], int]]:
        """
        预测所有7组号码

        Returns:
            {组号: (红球列表, 蓝球)}
        """
        results = {}

        results[1] = self.predict_group1_markov()
        results[2] = self.predict_group2_bayesian()
        results[3] = self.predict_group3_historical()
        results[4] = self.predict_group4_markov_big_following()
        results[5] = self.predict_group5_markov_cold_following()
        results[6] = self.predict_group6_markov_repeat_following()
        results[7] = self.predict_group7_markov_big_historical()

        return results

    def print_prediction_info(self):
        """
        打印预测过程中的相关信息
        """
        if not self.latest_info:
            print("无法获取最新期信息")
            return

        print(f"最新一期的号码为：{self.latest_info['期号']} {self.latest_info['红球']} + {self.latest_info['蓝球']}")

        # 基于历史出现概率预测的特征
        red_big_hist, blue_big_hist = self._get_predicted_big_ball_counts_historical()
        red_cold_hist, blue_cold_hist = self._get_predicted_cold_ball_counts_historical()

        print(f"基于历史出现概率预测的红蓝球大球数：{red_big_hist}、{blue_big_hist}")
        print(f"基于历史出现概率预测的红蓝球冷球数：{red_cold_hist}、{blue_cold_hist}")

        # 基于历史跟随性概率预测的特征
        red_big_follow, blue_big_follow = self._get_predicted_big_ball_counts_following()
        red_cold_follow, blue_cold_follow = self._get_predicted_cold_ball_counts_following()
        red_repeat_follow = self._get_predicted_repeat_count_following()

        print(f"基于历史跟随性概率预测的红蓝球大球数：{red_big_follow}、{blue_big_follow}")
        print(f"基于历史跟随性概率预测的红蓝球冷球数：{red_cold_follow}、{blue_cold_follow}")
        print(f"基于历史跟随性概率预测的红球重号数：{red_repeat_follow}")
        print(f"基于历史出现概率预测的蓝球冷球数：{blue_cold_hist}")


def test_prediction_algorithms():
    """测试预测算法功能"""
    from data_loader import DataLoader

    # 加载数据
    loader = DataLoader()
    data = loader.load_data()

    if data is None:
        print("数据加载失败")
        return

    # 创建概率计算器和预测算法
    prob_calc = ProbabilityCalculator(data)
    pred_algo = PredictionAlgorithms(prob_calc)

    print("=== 测试预测算法 ===")

    # 打印预测信息
    pred_algo.print_prediction_info()

    print("\n=== 7组预测结果 ===")

    # 预测所有组
    all_predictions = pred_algo.predict_all_groups()

    for group_num, (red_balls, blue_ball) in all_predictions.items():
        print(f"第{group_num}组预测号码为：{red_balls} + {blue_ball}")

    print("\n预测算法测试完成!")


if __name__ == "__main__":
    test_prediction_algorithms()
