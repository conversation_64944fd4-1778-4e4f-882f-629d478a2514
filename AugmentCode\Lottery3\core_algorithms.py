"""
核心算法模块
实现马尔科夫链和贝叶斯概率算法
"""

import numpy as np
import pandas as pd
from typing import Tuple, List
from probability_calculator import ProbabilityCalculator


class CoreAlgorithms:
    """核心算法类，实现马尔科夫链和贝叶斯概率算法"""
    
    def __init__(self, prob_calculator: ProbabilityCalculator):
        """
        初始化核心算法
        
        Args:
            prob_calculator: 概率计算器实例
        """
        self.prob_calculator = prob_calculator
        self.latest_info = prob_calculator.get_latest_period_info()
        
        # 获取概率表和矩阵
        self.red_prob_df, self.blue_prob_df = prob_calculator.calculate_ball_appearance_probability()
        self.red_follow_matrix, self.blue_follow_matrix = prob_calculator.calculate_ball_following_probability()
        
    def calculate_markov_chain_probabilities(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        计算马尔科夫链概率
        
        Returns:
            (红球马尔科夫链概率向量33x1, 蓝球马尔科夫链概率向量16x1)
        """
        if not self.latest_info:
            return np.zeros(33), np.zeros(16)
        
        # 获取最新一期的红球和蓝球号码
        latest_red_balls = self.latest_info['红球']
        latest_blue_ball = self.latest_info['蓝球']
        
        # 红球马尔科夫链计算
        # 1. 从跟随性概率矩阵中提取相关列
        red_columns = []
        for ball in latest_red_balls:
            red_columns.append(self.red_follow_matrix[:, ball-1])  # ball-1因为索引从0开始
        
        # 2. 将6列拼接成33x6矩阵
        red_matrix = np.column_stack(red_columns)  # 33x6
        
        # 3. 从历史出现概率中提取相关行
        red_appearance_probs = []
        for ball in latest_red_balls:
            prob = self.red_prob_df[self.red_prob_df['红球号码'] == ball]['历史出现概率'].iloc[0]
            red_appearance_probs.append(prob)
        
        red_vector = np.array(red_appearance_probs).reshape(-1, 1)  # 6x1
        
        # 4. 矩阵乘法：33x6 * 6x1 = 33x1
        red_markov_probs = red_matrix @ red_vector
        red_markov_probs = red_markov_probs.flatten()
        
        # 蓝球马尔科夫链计算
        # 直接使用跟随性概率矩阵的对应列
        blue_markov_probs = self.blue_follow_matrix[:, latest_blue_ball-1]
        
        return red_markov_probs, blue_markov_probs
    
    def calculate_bayesian_probabilities(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        计算贝叶斯概率
        
        Returns:
            (红球贝叶斯概率向量33x1, 蓝球贝叶斯概率向量16x1)
        """
        if not self.latest_info:
            return np.zeros(33), np.zeros(16)
        
        # 获取最新一期的红球和蓝球号码
        latest_red_balls = self.latest_info['红球']
        latest_blue_ball = self.latest_info['蓝球']
        
        # 红球贝叶斯概率计算
        # 1. 从跟随性概率矩阵中提取相关行并相加
        red_rows_sum = np.zeros(33)
        for ball in latest_red_balls:
            red_rows_sum += self.red_follow_matrix[ball-1, :]  # ball-1因为索引从0开始
        
        # 2. 获取历史出现概率向量
        red_appearance_probs = self.red_prob_df['历史出现概率'].values
        
        # 3. 逐元素相乘
        red_bayesian_probs = red_rows_sum * red_appearance_probs
        
        # 蓝球贝叶斯概率计算
        # 1. 从跟随性概率矩阵中提取相关行
        blue_row = self.blue_follow_matrix[latest_blue_ball-1, :]
        
        # 2. 获取历史出现概率向量
        blue_appearance_probs = self.blue_prob_df['历史出现概率'].values
        
        # 3. 逐元素相乘
        blue_bayesian_probs = blue_row * blue_appearance_probs
        
        return red_bayesian_probs, blue_bayesian_probs
    
    def get_top_probabilities(self, probabilities: np.ndarray, top_n: int) -> List[Tuple[int, float]]:
        """
        获取概率最高的前N个号码
        
        Args:
            probabilities: 概率数组
            top_n: 返回前N个
            
        Returns:
            [(号码, 概率), ...] 按概率降序排列
        """
        # 获取排序后的索引（降序）
        sorted_indices = np.argsort(probabilities)[::-1]
        
        # 返回前top_n个
        result = []
        for i in range(min(top_n, len(sorted_indices))):
            idx = sorted_indices[i]
            ball_number = idx + 1  # 号码从1开始
            probability = probabilities[idx]
            result.append((ball_number, probability))
        
        return result
    
    def predict_markov_numbers(self) -> Tuple[List[int], int]:
        """
        使用马尔科夫链预测号码
        
        Returns:
            (红球号码列表, 蓝球号码)
        """
        red_markov_probs, blue_markov_probs = self.calculate_markov_chain_probabilities()
        
        # 获取概率最高的6个红球和1个蓝球
        top_red = self.get_top_probabilities(red_markov_probs, 6)
        top_blue = self.get_top_probabilities(blue_markov_probs, 1)
        
        red_numbers = sorted([ball for ball, _ in top_red])
        blue_number = top_blue[0][0] if top_blue else 1
        
        return red_numbers, blue_number
    
    def predict_bayesian_numbers(self) -> Tuple[List[int], int]:
        """
        使用贝叶斯概率预测号码
        
        Returns:
            (红球号码列表, 蓝球号码)
        """
        red_bayesian_probs, blue_bayesian_probs = self.calculate_bayesian_probabilities()
        
        # 获取概率最高的6个红球和1个蓝球
        top_red = self.get_top_probabilities(red_bayesian_probs, 6)
        top_blue = self.get_top_probabilities(blue_bayesian_probs, 1)
        
        red_numbers = sorted([ball for ball, _ in top_red])
        blue_number = top_blue[0][0] if top_blue else 1
        
        return red_numbers, blue_number
    
    def predict_historical_numbers(self) -> Tuple[List[int], int]:
        """
        使用历史出现概率预测号码
        
        Returns:
            (红球号码列表, 蓝球号码)
        """
        # 获取概率最高的6个红球和1个蓝球
        red_probs = self.red_prob_df['历史出现概率'].values
        blue_probs = self.blue_prob_df['历史出现概率'].values
        
        top_red = self.get_top_probabilities(red_probs, 6)
        top_blue = self.get_top_probabilities(blue_probs, 1)
        
        red_numbers = sorted([ball for ball, _ in top_red])
        blue_number = top_blue[0][0] if top_blue else 1
        
        return red_numbers, blue_number


def test_core_algorithms():
    """测试核心算法功能"""
    from data_loader import DataLoader
    
    # 加载数据
    loader = DataLoader()
    data = loader.load_data()
    
    if data is None:
        print("数据加载失败")
        return
    
    # 创建概率计算器和核心算法
    prob_calc = ProbabilityCalculator(data)
    core_algo = CoreAlgorithms(prob_calc)
    
    print("=== 测试核心算法 ===")
    
    # 测试马尔科夫链
    red_markov, blue_markov = core_algo.calculate_markov_chain_probabilities()
    print(f"马尔科夫链概率计算完成")
    print(f"红球概率向量形状: {red_markov.shape}")
    print(f"蓝球概率向量形状: {blue_markov.shape}")
    
    # 测试贝叶斯概率
    red_bayesian, blue_bayesian = core_algo.calculate_bayesian_probabilities()
    print(f"贝叶斯概率计算完成")
    print(f"红球概率向量形状: {red_bayesian.shape}")
    print(f"蓝球概率向量形状: {blue_bayesian.shape}")
    
    # 测试预测
    markov_red, markov_blue = core_algo.predict_markov_numbers()
    bayesian_red, bayesian_blue = core_algo.predict_bayesian_numbers()
    historical_red, historical_blue = core_algo.predict_historical_numbers()
    
    print(f"\n马尔科夫链预测: 红球{markov_red} + 蓝球{markov_blue}")
    print(f"贝叶斯概率预测: 红球{bayesian_red} + 蓝球{bayesian_blue}")
    print(f"历史概率预测: 红球{historical_red} + 蓝球{historical_blue}")
    
    print("\n核心算法测试完成!")


if __name__ == "__main__":
    test_core_algorithms()
