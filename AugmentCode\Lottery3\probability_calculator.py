"""
概率统计模块
实现历史出现概率和历史跟随性概率的统计算法
"""

import pandas as pd
import numpy as np
from typing import Tuple, List
from collections import Counter


class ProbabilityCalculator:
    """概率计算器类，负责计算各种概率统计"""
    
    def __init__(self, data: pd.DataFrame):
        """
        初始化概率计算器
        
        Args:
            data: 彩票历史数据DataFrame
        """
        self.data = data
        self.red_ball_cols = [f'红球{i}' for i in range(1, 7)]
        self.blue_ball_col = '蓝球'
        
    def calculate_ball_appearance_probability(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        计算红球和蓝球号码的历史出现概率
        
        Returns:
            (红球概率表, 蓝球概率表)
        """
        # 红球号码出现概率
        red_balls = []
        for col in self.red_ball_cols:
            red_balls.extend(self.data[col].tolist())
        
        red_counter = Counter(red_balls)
        total_red = len(red_balls)
        
        red_prob_data = []
        for ball_num in range(1, 34):  # 红球1-33
            count = red_counter.get(ball_num, 0)
            probability = count / total_red if total_red > 0 else 0
            red_prob_data.append([ball_num, probability])
        
        red_prob_df = pd.DataFrame(red_prob_data, columns=['红球号码', '历史出现概率'])
        
        # 蓝球号码出现概率
        blue_balls = self.data[self.blue_ball_col].tolist()
        blue_counter = Counter(blue_balls)
        total_blue = len(blue_balls)
        
        blue_prob_data = []
        for ball_num in range(1, 17):  # 蓝球1-16
            count = blue_counter.get(ball_num, 0)
            probability = count / total_blue if total_blue > 0 else 0
            blue_prob_data.append([ball_num, probability])
        
        blue_prob_df = pd.DataFrame(blue_prob_data, columns=['蓝球号码', '历史出现概率'])
        
        return red_prob_df, blue_prob_df
    
    def calculate_big_ball_count_probability(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        计算红球和蓝球大球数的历史出现概率
        
        Returns:
            (红球大球数概率表, 蓝球大球数概率表)
        """
        # 计算每期的红球大球数（>16的红球个数）
        red_big_counts = []
        for _, row in self.data.iterrows():
            red_balls = [row[col] for col in self.red_ball_cols]
            big_count = sum(1 for ball in red_balls if ball > 16)
            red_big_counts.append(big_count)
        
        red_big_counter = Counter(red_big_counts)
        total_periods = len(red_big_counts)
        
        red_big_prob_data = []
        for count in range(7):  # 0-6个大球
            freq = red_big_counter.get(count, 0)
            probability = freq / total_periods if total_periods > 0 else 0
            red_big_prob_data.append([count, probability])
        
        red_big_prob_df = pd.DataFrame(red_big_prob_data, columns=['红球大球数', '历史出现概率'])
        
        # 计算每期的蓝球大球数（>8的蓝球个数）
        blue_big_counts = []
        for _, row in self.data.iterrows():
            blue_ball = row[self.blue_ball_col]
            big_count = 1 if blue_ball > 8 else 0
            blue_big_counts.append(big_count)
        
        blue_big_counter = Counter(blue_big_counts)
        
        blue_big_prob_data = []
        for count in range(2):  # 0-1个大球
            freq = blue_big_counter.get(count, 0)
            probability = freq / total_periods if total_periods > 0 else 0
            blue_big_prob_data.append([count, probability])
        
        blue_big_prob_df = pd.DataFrame(blue_big_prob_data, columns=['蓝球大球数', '历史出现概率'])
        
        return red_big_prob_df, blue_big_prob_df
    
    def calculate_cold_ball_count_probability(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        计算红球和蓝球冷球数的历史出现概率
        
        Returns:
            (红球冷球数概率表, 蓝球冷球数概率表)
        """
        red_cold_counts = []
        blue_cold_counts = []
        
        for i in range(len(self.data)):
            if i < 5:  # 前5期无法计算冷球数
                continue
                
            current_row = self.data.iloc[i]
            previous_5_rows = self.data.iloc[i-5:i]
            
            # 计算红球冷球数
            current_red_balls = set([current_row[col] for col in self.red_ball_cols])
            previous_red_balls = set()
            for _, prev_row in previous_5_rows.iterrows():
                previous_red_balls.update([prev_row[col] for col in self.red_ball_cols])
            
            red_cold_count = len(current_red_balls - previous_red_balls)
            red_cold_counts.append(red_cold_count)
            
            # 计算蓝球冷球数
            current_blue_ball = current_row[self.blue_ball_col]
            previous_blue_balls = set(previous_5_rows[self.blue_ball_col].tolist())
            
            blue_cold_count = 1 if current_blue_ball not in previous_blue_balls else 0
            blue_cold_counts.append(blue_cold_count)
        
        # 红球冷球数概率
        red_cold_counter = Counter(red_cold_counts)
        total_periods = len(red_cold_counts)
        
        red_cold_prob_data = []
        for count in range(7):  # 0-6个冷球
            freq = red_cold_counter.get(count, 0)
            probability = freq / total_periods if total_periods > 0 else 0
            red_cold_prob_data.append([count, probability])
        
        red_cold_prob_df = pd.DataFrame(red_cold_prob_data, columns=['红球冷球数', '历史出现概率'])
        
        # 蓝球冷球数概率
        blue_cold_counter = Counter(blue_cold_counts)
        
        blue_cold_prob_data = []
        for count in range(2):  # 0-1个冷球
            freq = blue_cold_counter.get(count, 0)
            probability = freq / total_periods if total_periods > 0 else 0
            blue_cold_prob_data.append([count, probability])
        
        blue_cold_prob_df = pd.DataFrame(blue_cold_prob_data, columns=['蓝球冷球数', '历史出现概率'])
        
        return red_cold_prob_df, blue_cold_prob_df
    
    def calculate_repeat_ball_count_probability(self) -> pd.DataFrame:
        """
        计算红球重号数的历史出现概率
        
        Returns:
            红球重号数概率表
        """
        repeat_counts = []
        
        for i in range(1, len(self.data)):  # 从第二期开始
            current_row = self.data.iloc[i]
            previous_row = self.data.iloc[i-1]
            
            current_red_balls = set([current_row[col] for col in self.red_ball_cols])
            previous_red_balls = set([previous_row[col] for col in self.red_ball_cols])
            
            repeat_count = len(current_red_balls & previous_red_balls)
            repeat_counts.append(repeat_count)
        
        repeat_counter = Counter(repeat_counts)
        total_periods = len(repeat_counts)
        
        repeat_prob_data = []
        for count in range(7):  # 0-6个重号
            freq = repeat_counter.get(count, 0)
            probability = freq / total_periods if total_periods > 0 else 0
            repeat_prob_data.append([count, probability])
        
        repeat_prob_df = pd.DataFrame(repeat_prob_data, columns=['红球重号数', '历史出现概率'])

        return repeat_prob_df

    def calculate_ball_following_probability(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        计算红球和蓝球号码的历史跟随性概率矩阵

        Returns:
            (红球跟随性概率矩阵33x33, 蓝球跟随性概率矩阵16x16)
        """
        # 红球跟随性概率矩阵 (33x33)
        red_follow_matrix = np.zeros((33, 33))

        for i in range(1, len(self.data)):
            current_row = self.data.iloc[i]
            previous_row = self.data.iloc[i-1]

            previous_red_balls = [previous_row[col] for col in self.red_ball_cols]
            current_red_balls = [current_row[col] for col in self.red_ball_cols]

            # 统计每个前期红球号码到当期红球号码的跟随次数
            for prev_ball in previous_red_balls:
                for curr_ball in current_red_balls:
                    red_follow_matrix[curr_ball-1, prev_ball-1] += 1

        # 转换为概率（每列和为1）
        for col in range(33):
            col_sum = red_follow_matrix[:, col].sum()
            if col_sum > 0:
                red_follow_matrix[:, col] /= col_sum

        # 蓝球跟随性概率矩阵 (16x16)
        blue_follow_matrix = np.zeros((16, 16))

        for i in range(1, len(self.data)):
            current_row = self.data.iloc[i]
            previous_row = self.data.iloc[i-1]

            previous_blue_ball = previous_row[self.blue_ball_col]
            current_blue_ball = current_row[self.blue_ball_col]

            blue_follow_matrix[current_blue_ball-1, previous_blue_ball-1] += 1

        # 转换为概率（每列和为1）
        for col in range(16):
            col_sum = blue_follow_matrix[:, col].sum()
            if col_sum > 0:
                blue_follow_matrix[:, col] /= col_sum

        return red_follow_matrix, blue_follow_matrix

    def calculate_big_ball_following_probability(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        计算红球和蓝球大球数的历史跟随性概率矩阵

        Returns:
            (红球大球数跟随性概率矩阵7x7, 蓝球大球数跟随性概率矩阵2x2)
        """
        # 红球大球数跟随性概率矩阵 (7x7)
        red_big_follow_matrix = np.zeros((7, 7))

        for i in range(1, len(self.data)):
            current_row = self.data.iloc[i]
            previous_row = self.data.iloc[i-1]

            # 计算前期红球大球数
            prev_red_balls = [previous_row[col] for col in self.red_ball_cols]
            prev_big_count = sum(1 for ball in prev_red_balls if ball > 16)

            # 计算当期红球大球数
            curr_red_balls = [current_row[col] for col in self.red_ball_cols]
            curr_big_count = sum(1 for ball in curr_red_balls if ball > 16)

            red_big_follow_matrix[curr_big_count, prev_big_count] += 1

        # 转换为概率（每列和为1）
        for col in range(7):
            col_sum = red_big_follow_matrix[:, col].sum()
            if col_sum > 0:
                red_big_follow_matrix[:, col] /= col_sum

        # 蓝球大球数跟随性概率矩阵 (2x2)
        blue_big_follow_matrix = np.zeros((2, 2))

        for i in range(1, len(self.data)):
            current_row = self.data.iloc[i]
            previous_row = self.data.iloc[i-1]

            # 计算前期蓝球大球数
            prev_blue_ball = previous_row[self.blue_ball_col]
            prev_big_count = 1 if prev_blue_ball > 8 else 0

            # 计算当期蓝球大球数
            curr_blue_ball = current_row[self.blue_ball_col]
            curr_big_count = 1 if curr_blue_ball > 8 else 0

            blue_big_follow_matrix[curr_big_count, prev_big_count] += 1

        # 转换为概率（每列和为1）
        for col in range(2):
            col_sum = blue_big_follow_matrix[:, col].sum()
            if col_sum > 0:
                blue_big_follow_matrix[:, col] /= col_sum

        return red_big_follow_matrix, blue_big_follow_matrix

    def calculate_cold_ball_following_probability(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        计算红球和蓝球冷球数的历史跟随性概率矩阵

        Returns:
            (红球冷球数跟随性概率矩阵7x7, 蓝球冷球数跟随性概率矩阵2x2)
        """
        # 计算每期的冷球数
        red_cold_counts = []
        blue_cold_counts = []

        for i in range(len(self.data)):
            if i < 5:  # 前5期无法计算冷球数
                red_cold_counts.append(0)
                blue_cold_counts.append(0)
                continue

            current_row = self.data.iloc[i]
            previous_5_rows = self.data.iloc[i-5:i]

            # 计算红球冷球数
            current_red_balls = set([current_row[col] for col in self.red_ball_cols])
            previous_red_balls = set()
            for _, prev_row in previous_5_rows.iterrows():
                previous_red_balls.update([prev_row[col] for col in self.red_ball_cols])

            red_cold_count = len(current_red_balls - previous_red_balls)
            red_cold_counts.append(red_cold_count)

            # 计算蓝球冷球数
            current_blue_ball = current_row[self.blue_ball_col]
            previous_blue_balls = set(previous_5_rows[self.blue_ball_col].tolist())

            blue_cold_count = 1 if current_blue_ball not in previous_blue_balls else 0
            blue_cold_counts.append(blue_cold_count)

        # 红球冷球数跟随性概率矩阵 (7x7)
        red_cold_follow_matrix = np.zeros((7, 7))

        for i in range(6, len(self.data)):  # 从第7期开始（前期也需要有冷球数）
            prev_cold_count = red_cold_counts[i-1]
            curr_cold_count = red_cold_counts[i]
            red_cold_follow_matrix[curr_cold_count, prev_cold_count] += 1

        # 转换为概率（每列和为1）
        for col in range(7):
            col_sum = red_cold_follow_matrix[:, col].sum()
            if col_sum > 0:
                red_cold_follow_matrix[:, col] /= col_sum

        # 蓝球冷球数跟随性概率矩阵 (2x2)
        blue_cold_follow_matrix = np.zeros((2, 2))

        for i in range(6, len(self.data)):  # 从第7期开始
            prev_cold_count = blue_cold_counts[i-1]
            curr_cold_count = blue_cold_counts[i]
            blue_cold_follow_matrix[curr_cold_count, prev_cold_count] += 1

        # 转换为概率（每列和为1）
        for col in range(2):
            col_sum = blue_cold_follow_matrix[:, col].sum()
            if col_sum > 0:
                blue_cold_follow_matrix[:, col] /= col_sum

        return red_cold_follow_matrix, blue_cold_follow_matrix

    def calculate_repeat_ball_following_probability(self) -> Tuple[np.ndarray, List[int]]:
        """
        计算红球重号数的历史跟随性概率矩阵

        Returns:
            红球重号数跟随性概率矩阵
        """
        # 计算每期的重号数
        repeat_counts = []

        for i in range(len(self.data)):
            if i == 0:  # 第一期无法计算重号数
                repeat_counts.append(0)
                continue

            current_row = self.data.iloc[i]
            previous_row = self.data.iloc[i-1]

            current_red_balls = set([current_row[col] for col in self.red_ball_cols])
            previous_red_balls = set([previous_row[col] for col in self.red_ball_cols])

            repeat_count = len(current_red_balls & previous_red_balls)
            repeat_counts.append(repeat_count)

        # 获取实际出现的重号数范围
        unique_counts = sorted(set(repeat_counts))
        count_to_index = {count: i for i, count in enumerate(unique_counts)}
        matrix_size = len(unique_counts)

        # 重号数跟随性概率矩阵
        repeat_follow_matrix = np.zeros((matrix_size, matrix_size))

        for i in range(2, len(self.data)):  # 从第3期开始（前期也需要有重号数）
            prev_repeat_count = repeat_counts[i-1]
            curr_repeat_count = repeat_counts[i]

            prev_index = count_to_index[prev_repeat_count]
            curr_index = count_to_index[curr_repeat_count]

            repeat_follow_matrix[curr_index, prev_index] += 1

        # 转换为概率（每列和为1）
        for col in range(matrix_size):
            col_sum = repeat_follow_matrix[:, col].sum()
            if col_sum > 0:
                repeat_follow_matrix[:, col] /= col_sum

        return repeat_follow_matrix, unique_counts

    def get_latest_period_info(self) -> dict:
        """
        获取最新一期的详细信息

        Returns:
            包含最新一期各种特征的字典
        """
        if len(self.data) == 0:
            return {}

        latest_row = self.data.iloc[-1]

        # 基本信息
        period = latest_row['期号']
        red_balls = [int(latest_row[col]) for col in self.red_ball_cols]
        blue_ball = int(latest_row[self.blue_ball_col])

        # 大球数
        red_big_count = sum(1 for ball in red_balls if ball > 16)
        blue_big_count = 1 if blue_ball > 8 else 0

        # 冷球数（需要前5期数据）
        red_cold_count = 0
        blue_cold_count = 0
        if len(self.data) >= 6:
            previous_5_rows = self.data.iloc[-6:-1]

            current_red_balls = set(red_balls)
            previous_red_balls = set()
            for _, prev_row in previous_5_rows.iterrows():
                previous_red_balls.update([prev_row[col] for col in self.red_ball_cols])

            red_cold_count = len(current_red_balls - previous_red_balls)

            previous_blue_balls = set(previous_5_rows[self.blue_ball_col].tolist())
            blue_cold_count = 1 if blue_ball not in previous_blue_balls else 0

        # 重号数（需要前1期数据）
        red_repeat_count = 0
        if len(self.data) >= 2:
            previous_row = self.data.iloc[-2]
            current_red_balls = set(red_balls)
            previous_red_balls = set([previous_row[col] for col in self.red_ball_cols])
            red_repeat_count = len(current_red_balls & previous_red_balls)

        return {
            '期号': period,
            '红球': red_balls,
            '蓝球': blue_ball,
            '红球大球数': red_big_count,
            '蓝球大球数': blue_big_count,
            '红球冷球数': red_cold_count,
            '蓝球冷球数': blue_cold_count,
            '红球重号数': red_repeat_count
        }


def test_probability_calculator():
    """测试概率计算器功能"""
    from data_loader import DataLoader

    # 加载数据
    loader = DataLoader()
    data = loader.load_data()

    if data is None:
        print("数据加载失败")
        return

    # 创建概率计算器
    calc = ProbabilityCalculator(data)

    print("=== 测试概率计算器 ===")
    print(f"数据总期数: {len(data)}")

    # 测试最新期信息
    latest_info = calc.get_latest_period_info()
    print(f"\n最新一期信息:")
    for key, value in latest_info.items():
        print(f"  {key}: {value}")

    # 测试历史出现概率
    red_prob, blue_prob = calc.calculate_ball_appearance_probability()
    print(f"\n红球出现概率表前5行:")
    print(red_prob.head())
    print(f"\n蓝球出现概率表前5行:")
    print(blue_prob.head())

    # 测试大球数概率
    red_big_prob, blue_big_prob = calc.calculate_big_ball_count_probability()
    print(f"\n红球大球数概率表:")
    print(red_big_prob)
    print(f"\n蓝球大球数概率表:")
    print(blue_big_prob)

    # 测试跟随性概率矩阵
    red_follow, blue_follow = calc.calculate_ball_following_probability()
    print(f"\n红球跟随性概率矩阵形状: {red_follow.shape}")
    print(f"蓝球跟随性概率矩阵形状: {blue_follow.shape}")

    print("\n概率计算器测试完成!")


if __name__ == "__main__":
    test_probability_calculator()
